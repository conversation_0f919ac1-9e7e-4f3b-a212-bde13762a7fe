{"ast": null, "code": "export const {\n  hasOwnProperty\n} = Object.prototype;\nexport const arrayFromSet = Array.from || function (set) {\n  const array = [];\n  set.forEach(item => array.push(item));\n  return array;\n};\nexport function maybeUnsubscribe(entryOrDep) {\n  const {\n    unsubscribe\n  } = entryOrDep;\n  if (typeof unsubscribe === \"function\") {\n    entryOrDep.unsubscribe = void 0;\n    unsubscribe();\n  }\n}", "map": {"version": 3, "names": ["hasOwnProperty", "Object", "prototype", "arrayFromSet", "Array", "from", "set", "array", "for<PERSON>ach", "item", "push", "maybeUnsubscribe", "entryOrDep", "unsubscribe"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/optimism/lib/helpers.js"], "sourcesContent": ["export const { hasOwnProperty, } = Object.prototype;\nexport const arrayFromSet = Array.from ||\n    function (set) {\n        const array = [];\n        set.forEach(item => array.push(item));\n        return array;\n    };\nexport function maybeUnsubscribe(entryOrDep) {\n    const { unsubscribe } = entryOrDep;\n    if (typeof unsubscribe === \"function\") {\n        entryOrDep.unsubscribe = void 0;\n        unsubscribe();\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAM;EAAEA;AAAgB,CAAC,GAAGC,MAAM,CAACC,SAAS;AACnD,OAAO,MAAMC,YAAY,GAAGC,KAAK,CAACC,IAAI,IAClC,UAAUC,GAAG,EAAE;EACX,MAAMC,KAAK,GAAG,EAAE;EAChBD,GAAG,CAACE,OAAO,CAACC,IAAI,IAAIF,KAAK,CAACG,IAAI,CAACD,IAAI,CAAC,CAAC;EACrC,OAAOF,KAAK;AAChB,CAAC;AACL,OAAO,SAASI,gBAAgBA,CAACC,UAAU,EAAE;EACzC,MAAM;IAAEC;EAAY,CAAC,GAAGD,UAAU;EAClC,IAAI,OAAOC,WAAW,KAAK,UAAU,EAAE;IACnCD,UAAU,CAACC,WAAW,GAAG,KAAK,CAAC;IAC/BA,WAAW,CAAC,CAAC;EACjB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}

// export interface Notification {
//   id: string;
//   type: 'NEW_MESSAGE' | 'FRIEND_REQUEST' | 'GROUP_INVITE' | 'MESSAGE_REACTION';
//   content: string;
//   timestamp: Date | string;
//   isRead: boolean;
//   sender?: {
//     id: string;
//     username: string;
//     image?: string;
//   };
//   message?: {
//     id?: string;
//     content: string;
//     attachments?: Array<{
//       url: string;
//       type: 'IMAGE' | 'DOCUMENT' | 'AUDIO' | 'VIDEO' | 'OTHER';
//       name?: string;
//       size?: number;
//     }>;
//   };
// }

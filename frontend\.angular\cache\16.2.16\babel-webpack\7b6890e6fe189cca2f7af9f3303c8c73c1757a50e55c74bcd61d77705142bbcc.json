{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isFriday} function options.\n */\n\n/**\n * @name isFriday\n * @category Weekday Helpers\n * @summary Is the given date Friday?\n *\n * @description\n * Is the given date Friday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is Friday\n *\n * @example\n * // Is 26 September 2014 Friday?\n * const result = isFriday(new Date(2014, 8, 26))\n * //=> true\n */\nexport function isFriday(date, options) {\n  return toDate(date, options?.in).getDay() === 5;\n}\n\n// Fallback for modularized imports:\nexport default isFriday;", "map": {"version": 3, "names": ["toDate", "isFriday", "date", "options", "in", "getDay"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/date-fns/isFriday.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isFriday} function options.\n */\n\n/**\n * @name isFriday\n * @category Weekday Helpers\n * @summary Is the given date Friday?\n *\n * @description\n * Is the given date Friday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is Friday\n *\n * @example\n * // Is 26 September 2014 Friday?\n * const result = isFriday(new Date(2014, 8, 26))\n * //=> true\n */\nexport function isFriday(date, options) {\n  return toDate(date, options?.in).getDay() === 5;\n}\n\n// Fallback for modularized imports:\nexport default isFriday;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACtC,OAAOH,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,KAAK,CAAC;AACjD;;AAEA;AACA,eAAeJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/**\n * Given an AsyncIterable and a callback function, return an AsyncIterator\n * which produces values mapped via calling the callback function.\n */\nexport function mapAsyncIterator(iterable, callback) {\n  const iterator = iterable[Symbol.asyncIterator]();\n  function mapResult(_x) {\n    return _mapResult.apply(this, arguments);\n  }\n  function _mapResult() {\n    _mapResult = _asyncToGenerator(function* (result) {\n      if (result.done) {\n        return result;\n      }\n      try {\n        return {\n          value: yield callback(result.value),\n          done: false\n        };\n      } catch (error) {\n        /* c8 ignore start */\n        // FIXME: add test case\n        if (typeof iterator.return === 'function') {\n          try {\n            yield iterator.return();\n          } catch (_e) {\n            /* ignore error */\n          }\n        }\n        throw error;\n        /* c8 ignore stop */\n      }\n    });\n    return _mapResult.apply(this, arguments);\n  }\n  return {\n    next() {\n      return _asyncToGenerator(function* () {\n        return mapResult(yield iterator.next());\n      })();\n    },\n    return() {\n      return _asyncToGenerator(function* () {\n        // If iterator.return() does not exist, then type R must be undefined.\n        return typeof iterator.return === 'function' ? mapResult(yield iterator.return()) : {\n          value: undefined,\n          done: true\n        };\n      })();\n    },\n    throw(error) {\n      return _asyncToGenerator(function* () {\n        if (typeof iterator.throw === 'function') {\n          return mapResult(yield iterator.throw(error));\n        }\n        throw error;\n      })();\n    },\n    [Symbol.asyncIterator]() {\n      return this;\n    }\n  };\n}", "map": {"version": 3, "names": ["mapAsyncIterator", "iterable", "callback", "iterator", "Symbol", "asyncIterator", "mapResult", "_x", "_mapResult", "apply", "arguments", "_asyncToGenerator", "result", "done", "value", "error", "return", "_e", "next", "undefined", "throw"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/execution/mapAsyncIterator.mjs"], "sourcesContent": ["/**\n * Given an AsyncIterable and a callback function, return an AsyncIterator\n * which produces values mapped via calling the callback function.\n */\nexport function mapAsyncIterator(iterable, callback) {\n  const iterator = iterable[Symbol.asyncIterator]();\n\n  async function mapResult(result) {\n    if (result.done) {\n      return result;\n    }\n\n    try {\n      return {\n        value: await callback(result.value),\n        done: false,\n      };\n    } catch (error) {\n      /* c8 ignore start */\n      // FIXME: add test case\n      if (typeof iterator.return === 'function') {\n        try {\n          await iterator.return();\n        } catch (_e) {\n          /* ignore error */\n        }\n      }\n\n      throw error;\n      /* c8 ignore stop */\n    }\n  }\n\n  return {\n    async next() {\n      return mapResult(await iterator.next());\n    },\n\n    async return() {\n      // If iterator.return() does not exist, then type R must be undefined.\n      return typeof iterator.return === 'function'\n        ? mapResult(await iterator.return())\n        : {\n            value: undefined,\n            done: true,\n          };\n    },\n\n    async throw(error) {\n      if (typeof iterator.throw === 'function') {\n        return mapResult(await iterator.throw(error));\n      }\n\n      throw error;\n    },\n\n    [Symbol.asyncIterator]() {\n      return this;\n    },\n  };\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,gBAAgBA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EACnD,MAAMC,QAAQ,GAAGF,QAAQ,CAACG,MAAM,CAACC,aAAa,CAAC,CAAC,CAAC;EAAC,SAEnCC,SAASA,CAAAC,EAAA;IAAA,OAAAC,UAAA,CAAAC,KAAA,OAAAC,SAAA;EAAA;EAAA,SAAAF,WAAA;IAAAA,UAAA,GAAAG,iBAAA,CAAxB,WAAyBC,MAAM,EAAE;MAC/B,IAAIA,MAAM,CAACC,IAAI,EAAE;QACf,OAAOD,MAAM;MACf;MAEA,IAAI;QACF,OAAO;UACLE,KAAK,QAAQZ,QAAQ,CAACU,MAAM,CAACE,KAAK,CAAC;UACnCD,IAAI,EAAE;QACR,CAAC;MACH,CAAC,CAAC,OAAOE,KAAK,EAAE;QACd;QACA;QACA,IAAI,OAAOZ,QAAQ,CAACa,MAAM,KAAK,UAAU,EAAE;UACzC,IAAI;YACF,MAAMb,QAAQ,CAACa,MAAM,CAAC,CAAC;UACzB,CAAC,CAAC,OAAOC,EAAE,EAAE;YACX;UAAA;QAEJ;QAEA,MAAMF,KAAK;QACX;MACF;IACF,CAAC;IAAA,OAAAP,UAAA,CAAAC,KAAA,OAAAC,SAAA;EAAA;EAED,OAAO;IACCQ,IAAIA,CAAA,EAAG;MAAA,OAAAP,iBAAA;QACX,OAAOL,SAAS,OAAOH,QAAQ,CAACe,IAAI,CAAC,CAAC,CAAC;MAAC;IAC1C,CAAC;IAEKF,MAAMA,CAAA,EAAG;MAAA,OAAAL,iBAAA;QACb;QACA,OAAO,OAAOR,QAAQ,CAACa,MAAM,KAAK,UAAU,GACxCV,SAAS,OAAOH,QAAQ,CAACa,MAAM,CAAC,CAAC,CAAC,GAClC;UACEF,KAAK,EAAEK,SAAS;UAChBN,IAAI,EAAE;QACR,CAAC;MAAC;IACR,CAAC;IAEKO,KAAKA,CAACL,KAAK,EAAE;MAAA,OAAAJ,iBAAA;QACjB,IAAI,OAAOR,QAAQ,CAACiB,KAAK,KAAK,UAAU,EAAE;UACxC,OAAOd,SAAS,OAAOH,QAAQ,CAACiB,KAAK,CAACL,KAAK,CAAC,CAAC;QAC/C;QAEA,MAAMA,KAAK;MAAC;IACd,CAAC;IAED,CAACX,MAAM,CAACC,aAAa,IAAI;MACvB,OAAO,IAAI;IACb;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
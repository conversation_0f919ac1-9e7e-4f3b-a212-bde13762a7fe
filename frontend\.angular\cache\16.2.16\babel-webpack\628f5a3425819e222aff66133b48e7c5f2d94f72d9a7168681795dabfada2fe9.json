{"ast": null, "code": "import { Kind } from '../language/kinds.mjs';\nimport { isAbstractType } from '../type/definition.mjs';\nimport { GraphQLIncludeDirective, GraphQLSkipDirective } from '../type/directives.mjs';\nimport { typeFromAST } from '../utilities/typeFromAST.mjs';\nimport { getDirectiveValues } from './values.mjs';\n/**\n * Given a selectionSet, collects all of the fields and returns them.\n *\n * CollectFields requires the \"runtime type\" of an object. For a field that\n * returns an Interface or Union type, the \"runtime type\" will be the actual\n * object type returned by that field.\n *\n * @internal\n */\n\nexport function collectFields(schema, fragments, variableValues, runtimeType, selectionSet) {\n  const fields = new Map();\n  collectFieldsImpl(schema, fragments, variableValues, runtimeType, selectionSet, fields, new Set());\n  return fields;\n}\n/**\n * Given an array of field nodes, collects all of the subfields of the passed\n * in fields, and returns them at the end.\n *\n * CollectSubFields requires the \"return type\" of an object. For a field that\n * returns an Interface or Union type, the \"return type\" will be the actual\n * object type returned by that field.\n *\n * @internal\n */\n\nexport function collectSubfields(schema, fragments, variableValues, returnType, fieldNodes) {\n  const subFieldNodes = new Map();\n  const visitedFragmentNames = new Set();\n  for (const node of fieldNodes) {\n    if (node.selectionSet) {\n      collectFieldsImpl(schema, fragments, variableValues, returnType, node.selectionSet, subFieldNodes, visitedFragmentNames);\n    }\n  }\n  return subFieldNodes;\n}\nfunction collectFieldsImpl(schema, fragments, variableValues, runtimeType, selectionSet, fields, visitedFragmentNames) {\n  for (const selection of selectionSet.selections) {\n    switch (selection.kind) {\n      case Kind.FIELD:\n        {\n          if (!shouldIncludeNode(variableValues, selection)) {\n            continue;\n          }\n          const name = getFieldEntryKey(selection);\n          const fieldList = fields.get(name);\n          if (fieldList !== undefined) {\n            fieldList.push(selection);\n          } else {\n            fields.set(name, [selection]);\n          }\n          break;\n        }\n      case Kind.INLINE_FRAGMENT:\n        {\n          if (!shouldIncludeNode(variableValues, selection) || !doesFragmentConditionMatch(schema, selection, runtimeType)) {\n            continue;\n          }\n          collectFieldsImpl(schema, fragments, variableValues, runtimeType, selection.selectionSet, fields, visitedFragmentNames);\n          break;\n        }\n      case Kind.FRAGMENT_SPREAD:\n        {\n          const fragName = selection.name.value;\n          if (visitedFragmentNames.has(fragName) || !shouldIncludeNode(variableValues, selection)) {\n            continue;\n          }\n          visitedFragmentNames.add(fragName);\n          const fragment = fragments[fragName];\n          if (!fragment || !doesFragmentConditionMatch(schema, fragment, runtimeType)) {\n            continue;\n          }\n          collectFieldsImpl(schema, fragments, variableValues, runtimeType, fragment.selectionSet, fields, visitedFragmentNames);\n          break;\n        }\n    }\n  }\n}\n/**\n * Determines if a field should be included based on the `@include` and `@skip`\n * directives, where `@skip` has higher precedence than `@include`.\n */\n\nfunction shouldIncludeNode(variableValues, node) {\n  const skip = getDirectiveValues(GraphQLSkipDirective, node, variableValues);\n  if ((skip === null || skip === void 0 ? void 0 : skip.if) === true) {\n    return false;\n  }\n  const include = getDirectiveValues(GraphQLIncludeDirective, node, variableValues);\n  if ((include === null || include === void 0 ? void 0 : include.if) === false) {\n    return false;\n  }\n  return true;\n}\n/**\n * Determines if a fragment is applicable to the given type.\n */\n\nfunction doesFragmentConditionMatch(schema, fragment, type) {\n  const typeConditionNode = fragment.typeCondition;\n  if (!typeConditionNode) {\n    return true;\n  }\n  const conditionalType = typeFromAST(schema, typeConditionNode);\n  if (conditionalType === type) {\n    return true;\n  }\n  if (isAbstractType(conditionalType)) {\n    return schema.isSubType(conditionalType, type);\n  }\n  return false;\n}\n/**\n * Implements the logic to compute the key of a given field's entry\n */\n\nfunction getFieldEntryKey(node) {\n  return node.alias ? node.alias.value : node.name.value;\n}", "map": {"version": 3, "names": ["Kind", "isAbstractType", "GraphQLIncludeDirective", "GraphQLSkipDirective", "typeFromAST", "getDirectiveValues", "collectFields", "schema", "fragments", "variableValues", "runtimeType", "selectionSet", "fields", "Map", "collectFieldsImpl", "Set", "collectSubfields", "returnType", "fieldNodes", "subFieldNodes", "visitedFragmentNames", "node", "selection", "selections", "kind", "FIELD", "shouldIncludeNode", "name", "getFieldEntryKey", "fieldList", "get", "undefined", "push", "set", "INLINE_FRAGMENT", "doesFragmentConditionMatch", "FRAGMENT_SPREAD", "fragName", "value", "has", "add", "fragment", "skip", "if", "include", "type", "typeConditionNode", "typeCondition", "conditionalType", "isSubType", "alias"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/execution/collectFields.mjs"], "sourcesContent": ["import { Kind } from '../language/kinds.mjs';\nimport { isAbstractType } from '../type/definition.mjs';\nimport {\n  GraphQLIncludeDirective,\n  GraphQLSkipDirective,\n} from '../type/directives.mjs';\nimport { typeFromAST } from '../utilities/typeFromAST.mjs';\nimport { getDirectiveValues } from './values.mjs';\n/**\n * Given a selectionSet, collects all of the fields and returns them.\n *\n * CollectFields requires the \"runtime type\" of an object. For a field that\n * returns an Interface or Union type, the \"runtime type\" will be the actual\n * object type returned by that field.\n *\n * @internal\n */\n\nexport function collectFields(\n  schema,\n  fragments,\n  variableValues,\n  runtimeType,\n  selectionSet,\n) {\n  const fields = new Map();\n  collectFieldsImpl(\n    schema,\n    fragments,\n    variableValues,\n    runtimeType,\n    selectionSet,\n    fields,\n    new Set(),\n  );\n  return fields;\n}\n/**\n * Given an array of field nodes, collects all of the subfields of the passed\n * in fields, and returns them at the end.\n *\n * CollectSubFields requires the \"return type\" of an object. For a field that\n * returns an Interface or Union type, the \"return type\" will be the actual\n * object type returned by that field.\n *\n * @internal\n */\n\nexport function collectSubfields(\n  schema,\n  fragments,\n  variableValues,\n  returnType,\n  fieldNodes,\n) {\n  const subFieldNodes = new Map();\n  const visitedFragmentNames = new Set();\n\n  for (const node of fieldNodes) {\n    if (node.selectionSet) {\n      collectFieldsImpl(\n        schema,\n        fragments,\n        variableValues,\n        returnType,\n        node.selectionSet,\n        subFieldNodes,\n        visitedFragmentNames,\n      );\n    }\n  }\n\n  return subFieldNodes;\n}\n\nfunction collectFieldsImpl(\n  schema,\n  fragments,\n  variableValues,\n  runtimeType,\n  selectionSet,\n  fields,\n  visitedFragmentNames,\n) {\n  for (const selection of selectionSet.selections) {\n    switch (selection.kind) {\n      case Kind.FIELD: {\n        if (!shouldIncludeNode(variableValues, selection)) {\n          continue;\n        }\n\n        const name = getFieldEntryKey(selection);\n        const fieldList = fields.get(name);\n\n        if (fieldList !== undefined) {\n          fieldList.push(selection);\n        } else {\n          fields.set(name, [selection]);\n        }\n\n        break;\n      }\n\n      case Kind.INLINE_FRAGMENT: {\n        if (\n          !shouldIncludeNode(variableValues, selection) ||\n          !doesFragmentConditionMatch(schema, selection, runtimeType)\n        ) {\n          continue;\n        }\n\n        collectFieldsImpl(\n          schema,\n          fragments,\n          variableValues,\n          runtimeType,\n          selection.selectionSet,\n          fields,\n          visitedFragmentNames,\n        );\n        break;\n      }\n\n      case Kind.FRAGMENT_SPREAD: {\n        const fragName = selection.name.value;\n\n        if (\n          visitedFragmentNames.has(fragName) ||\n          !shouldIncludeNode(variableValues, selection)\n        ) {\n          continue;\n        }\n\n        visitedFragmentNames.add(fragName);\n        const fragment = fragments[fragName];\n\n        if (\n          !fragment ||\n          !doesFragmentConditionMatch(schema, fragment, runtimeType)\n        ) {\n          continue;\n        }\n\n        collectFieldsImpl(\n          schema,\n          fragments,\n          variableValues,\n          runtimeType,\n          fragment.selectionSet,\n          fields,\n          visitedFragmentNames,\n        );\n        break;\n      }\n    }\n  }\n}\n/**\n * Determines if a field should be included based on the `@include` and `@skip`\n * directives, where `@skip` has higher precedence than `@include`.\n */\n\nfunction shouldIncludeNode(variableValues, node) {\n  const skip = getDirectiveValues(GraphQLSkipDirective, node, variableValues);\n\n  if ((skip === null || skip === void 0 ? void 0 : skip.if) === true) {\n    return false;\n  }\n\n  const include = getDirectiveValues(\n    GraphQLIncludeDirective,\n    node,\n    variableValues,\n  );\n\n  if (\n    (include === null || include === void 0 ? void 0 : include.if) === false\n  ) {\n    return false;\n  }\n\n  return true;\n}\n/**\n * Determines if a fragment is applicable to the given type.\n */\n\nfunction doesFragmentConditionMatch(schema, fragment, type) {\n  const typeConditionNode = fragment.typeCondition;\n\n  if (!typeConditionNode) {\n    return true;\n  }\n\n  const conditionalType = typeFromAST(schema, typeConditionNode);\n\n  if (conditionalType === type) {\n    return true;\n  }\n\n  if (isAbstractType(conditionalType)) {\n    return schema.isSubType(conditionalType, type);\n  }\n\n  return false;\n}\n/**\n * Implements the logic to compute the key of a given field's entry\n */\n\nfunction getFieldEntryKey(node) {\n  return node.alias ? node.alias.value : node.name.value;\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,uBAAuB;AAC5C,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SACEC,uBAAuB,EACvBC,oBAAoB,QACf,wBAAwB;AAC/B,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,kBAAkB,QAAQ,cAAc;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,aAAaA,CAC3BC,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,WAAW,EACXC,YAAY,EACZ;EACA,MAAMC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;EACxBC,iBAAiB,CACfP,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,WAAW,EACXC,YAAY,EACZC,MAAM,EACN,IAAIG,GAAG,CAAC,CACV,CAAC;EACD,OAAOH,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASI,gBAAgBA,CAC9BT,MAAM,EACNC,SAAS,EACTC,cAAc,EACdQ,UAAU,EACVC,UAAU,EACV;EACA,MAAMC,aAAa,GAAG,IAAIN,GAAG,CAAC,CAAC;EAC/B,MAAMO,oBAAoB,GAAG,IAAIL,GAAG,CAAC,CAAC;EAEtC,KAAK,MAAMM,IAAI,IAAIH,UAAU,EAAE;IAC7B,IAAIG,IAAI,CAACV,YAAY,EAAE;MACrBG,iBAAiB,CACfP,MAAM,EACNC,SAAS,EACTC,cAAc,EACdQ,UAAU,EACVI,IAAI,CAACV,YAAY,EACjBQ,aAAa,EACbC,oBACF,CAAC;IACH;EACF;EAEA,OAAOD,aAAa;AACtB;AAEA,SAASL,iBAAiBA,CACxBP,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,WAAW,EACXC,YAAY,EACZC,MAAM,EACNQ,oBAAoB,EACpB;EACA,KAAK,MAAME,SAAS,IAAIX,YAAY,CAACY,UAAU,EAAE;IAC/C,QAAQD,SAAS,CAACE,IAAI;MACpB,KAAKxB,IAAI,CAACyB,KAAK;QAAE;UACf,IAAI,CAACC,iBAAiB,CAACjB,cAAc,EAAEa,SAAS,CAAC,EAAE;YACjD;UACF;UAEA,MAAMK,IAAI,GAAGC,gBAAgB,CAACN,SAAS,CAAC;UACxC,MAAMO,SAAS,GAAGjB,MAAM,CAACkB,GAAG,CAACH,IAAI,CAAC;UAElC,IAAIE,SAAS,KAAKE,SAAS,EAAE;YAC3BF,SAAS,CAACG,IAAI,CAACV,SAAS,CAAC;UAC3B,CAAC,MAAM;YACLV,MAAM,CAACqB,GAAG,CAACN,IAAI,EAAE,CAACL,SAAS,CAAC,CAAC;UAC/B;UAEA;QACF;MAEA,KAAKtB,IAAI,CAACkC,eAAe;QAAE;UACzB,IACE,CAACR,iBAAiB,CAACjB,cAAc,EAAEa,SAAS,CAAC,IAC7C,CAACa,0BAA0B,CAAC5B,MAAM,EAAEe,SAAS,EAAEZ,WAAW,CAAC,EAC3D;YACA;UACF;UAEAI,iBAAiB,CACfP,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,WAAW,EACXY,SAAS,CAACX,YAAY,EACtBC,MAAM,EACNQ,oBACF,CAAC;UACD;QACF;MAEA,KAAKpB,IAAI,CAACoC,eAAe;QAAE;UACzB,MAAMC,QAAQ,GAAGf,SAAS,CAACK,IAAI,CAACW,KAAK;UAErC,IACElB,oBAAoB,CAACmB,GAAG,CAACF,QAAQ,CAAC,IAClC,CAACX,iBAAiB,CAACjB,cAAc,EAAEa,SAAS,CAAC,EAC7C;YACA;UACF;UAEAF,oBAAoB,CAACoB,GAAG,CAACH,QAAQ,CAAC;UAClC,MAAMI,QAAQ,GAAGjC,SAAS,CAAC6B,QAAQ,CAAC;UAEpC,IACE,CAACI,QAAQ,IACT,CAACN,0BAA0B,CAAC5B,MAAM,EAAEkC,QAAQ,EAAE/B,WAAW,CAAC,EAC1D;YACA;UACF;UAEAI,iBAAiB,CACfP,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,WAAW,EACX+B,QAAQ,CAAC9B,YAAY,EACrBC,MAAM,EACNQ,oBACF,CAAC;UACD;QACF;IACF;EACF;AACF;AACA;AACA;AACA;AACA;;AAEA,SAASM,iBAAiBA,CAACjB,cAAc,EAAEY,IAAI,EAAE;EAC/C,MAAMqB,IAAI,GAAGrC,kBAAkB,CAACF,oBAAoB,EAAEkB,IAAI,EAAEZ,cAAc,CAAC;EAE3E,IAAI,CAACiC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACC,EAAE,MAAM,IAAI,EAAE;IAClE,OAAO,KAAK;EACd;EAEA,MAAMC,OAAO,GAAGvC,kBAAkB,CAChCH,uBAAuB,EACvBmB,IAAI,EACJZ,cACF,CAAC;EAED,IACE,CAACmC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACD,EAAE,MAAM,KAAK,EACxE;IACA,OAAO,KAAK;EACd;EAEA,OAAO,IAAI;AACb;AACA;AACA;AACA;;AAEA,SAASR,0BAA0BA,CAAC5B,MAAM,EAAEkC,QAAQ,EAAEI,IAAI,EAAE;EAC1D,MAAMC,iBAAiB,GAAGL,QAAQ,CAACM,aAAa;EAEhD,IAAI,CAACD,iBAAiB,EAAE;IACtB,OAAO,IAAI;EACb;EAEA,MAAME,eAAe,GAAG5C,WAAW,CAACG,MAAM,EAAEuC,iBAAiB,CAAC;EAE9D,IAAIE,eAAe,KAAKH,IAAI,EAAE;IAC5B,OAAO,IAAI;EACb;EAEA,IAAI5C,cAAc,CAAC+C,eAAe,CAAC,EAAE;IACnC,OAAOzC,MAAM,CAAC0C,SAAS,CAACD,eAAe,EAAEH,IAAI,CAAC;EAChD;EAEA,OAAO,KAAK;AACd;AACA;AACA;AACA;;AAEA,SAASjB,gBAAgBA,CAACP,IAAI,EAAE;EAC9B,OAAOA,IAAI,CAAC6B,KAAK,GAAG7B,IAAI,CAAC6B,KAAK,CAACZ,KAAK,GAAGjB,IAAI,CAACM,IAAI,CAACW,KAAK;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
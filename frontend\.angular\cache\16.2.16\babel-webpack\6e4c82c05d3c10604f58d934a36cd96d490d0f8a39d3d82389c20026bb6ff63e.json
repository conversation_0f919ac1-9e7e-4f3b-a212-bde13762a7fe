{"ast": null, "code": "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\nexport class Hour1to12Parser extends Parser {\n  priority = 70;\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"h\":\n        return parseNumericPattern(numericPatterns.hour12h, dateString);\n      case \"ho\":\n        return match.ordinalNumber(dateString, {\n          unit: \"hour\"\n        });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n  validate(_date, value) {\n    return value >= 1 && value <= 12;\n  }\n  set(date, _flags, value) {\n    const isPM = date.getHours() >= 12;\n    if (isPM && value < 12) {\n      date.setHours(value + 12, 0, 0, 0);\n    } else if (!isPM && value === 12) {\n      date.setHours(0, 0, 0, 0);\n    } else {\n      date.setHours(value, 0, 0, 0);\n    }\n    return date;\n  }\n  incompatibleTokens = [\"H\", \"K\", \"k\", \"t\", \"T\"];\n}", "map": {"version": 3, "names": ["numericPatterns", "<PERSON><PERSON><PERSON>", "parseNDigits", "parseNumericPattern", "Hour1to12<PERSON><PERSON><PERSON>", "priority", "parse", "dateString", "token", "match", "hour12h", "ordinalNumber", "unit", "length", "validate", "_date", "value", "set", "date", "_flags", "isPM", "getHours", "setHours", "incompatibleTokens"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/date-fns/parse/_lib/parsers/Hour1to12Parser.js"], "sourcesContent": ["import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class Hour1to12Parser extends Parser {\n  priority = 70;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"h\":\n        return parseNumericPattern(numericPatterns.hour12h, dateString);\n      case \"ho\":\n        return match.ordinalNumber(dateString, { unit: \"hour\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 1 && value <= 12;\n  }\n\n  set(date, _flags, value) {\n    const isPM = date.getHours() >= 12;\n    if (isPM && value < 12) {\n      date.setHours(value + 12, 0, 0, 0);\n    } else if (!isPM && value === 12) {\n      date.setHours(0, 0, 0, 0);\n    } else {\n      date.setHours(value, 0, 0, 0);\n    }\n    return date;\n  }\n\n  incompatibleTokens = [\"H\", \"K\", \"k\", \"t\", \"T\"];\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,iBAAiB;AACjD,SAASC,MAAM,QAAQ,cAAc;AAErC,SAASC,YAAY,EAAEC,mBAAmB,QAAQ,aAAa;AAE/D,OAAO,MAAMC,eAAe,SAASH,MAAM,CAAC;EAC1CI,QAAQ,GAAG,EAAE;EAEbC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC9B,QAAQD,KAAK;MACX,KAAK,GAAG;QACN,OAAOL,mBAAmB,CAACH,eAAe,CAACU,OAAO,EAAEH,UAAU,CAAC;MACjE,KAAK,IAAI;QACP,OAAOE,KAAK,CAACE,aAAa,CAACJ,UAAU,EAAE;UAAEK,IAAI,EAAE;QAAO,CAAC,CAAC;MAC1D;QACE,OAAOV,YAAY,CAACM,KAAK,CAACK,MAAM,EAAEN,UAAU,CAAC;IACjD;EACF;EAEAO,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACrB,OAAOA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,EAAE;EAClC;EAEAC,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEH,KAAK,EAAE;IACvB,MAAMI,IAAI,GAAGF,IAAI,CAACG,QAAQ,CAAC,CAAC,IAAI,EAAE;IAClC,IAAID,IAAI,IAAIJ,KAAK,GAAG,EAAE,EAAE;MACtBE,IAAI,CAACI,QAAQ,CAACN,KAAK,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACpC,CAAC,MAAM,IAAI,CAACI,IAAI,IAAIJ,KAAK,KAAK,EAAE,EAAE;MAChCE,IAAI,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC3B,CAAC,MAAM;MACLJ,IAAI,CAACI,QAAQ,CAACN,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/B;IACA,OAAOE,IAAI;EACb;EAEAK,kBAAkB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
.evaluation-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.score-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.score-item {
  display: flex;
  flex-direction: column;
}

.score-total {
  margin-top: 20px;
  font-weight: bold;
  font-size: 1.2em;
}

.actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  margin: 50px 0;
}

.error-message {
  color: #f44336;
  margin: 10px 0;
}
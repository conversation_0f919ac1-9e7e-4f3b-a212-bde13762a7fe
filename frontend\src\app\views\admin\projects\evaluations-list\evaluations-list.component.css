.evaluations-container {
  padding: 20px;
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 5px;
}

.filter-item {
  flex: 1;
  min-width: 200px;
}

.evaluations-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.evaluations-table th, 
.evaluations-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.evaluations-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.evaluations-table tr:hover {
  background-color: #f5f5f5;
}

.actions-cell {
  display: flex;
  gap: 10px;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  margin: 50px 0;
}

.no-evaluations {
  text-align: center;
  margin: 50px 0;
  color: #666;
}

.score-badge {
  padding: 5px 10px;
  border-radius: 15px;
  font-weight: bold;
  display: inline-block;
}

.score-high {
  background-color: #c8e6c9;
  color: #2e7d32;
}

.score-medium {
  background-color: #fff9c4;
  color: #f57f17;
}

.score-low {
  background-color: #ffcdd2;
  color: #c62828;
}
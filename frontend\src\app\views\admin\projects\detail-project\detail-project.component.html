<div class="max-w-6xl mx-auto my-8 space-y-6">
  <!-- Header avec stats -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Carte principale -->
    <div class="md:col-span-2 bg-white rounded-2xl shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden border border-[#e4e7ec]">
      <div class="bg-gradient-to-r from-[#6C63FF] to-[#C77DFF] p-6 text-white rounded-t-2xl">
        <div class="flex justify-between items-start">
          <div>
            <h3 class="text-2xl font-bold">{{ projet?.titre }}</h3>
            <p class="mt-1 text-sm text-[#e4dbf8]">
              Groupe: {{ projet?.groupe }} • Date limite: {{ projet?.dateLimite ? formatDate(projet?.dateLimite) : '' }}
            </p>
          </div>
          <span class="px-3 py-1 bg-white/20 rounded-full text-xs font-semibold">
            {{ projet?.statut || 'En cours' }}
          </span>
        </div>
      </div>
      <div class="p-6 space-y-6">
        <div>
          <h4 class="text-sm font-semibold text-gray-600 mb-2">Description</h4>
          <p class="text-gray-700">{{ projet?.description || 'Aucune description fournie' }}</p>
        </div>

        <div *ngIf="projet?.fichiers?.length > 0">
          <h4 class="text-sm font-semibold text-gray-600 mb-3">Fichiers joints</h4>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div *ngFor="let file of projet.fichiers" class="flex items-center">
              <a [href]="getFileUrl(file)" download
                class="flex-1 text-center text-sm text-white bg-gradient-to-r from-[#8E2DE2] to-[#4A00E0] hover:from-[#7c22d2] hover:to-[#3f00cc] rounded-lg py-2 px-4 transition-all hover:scale-[1.02] flex items-center justify-center space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                <span>Télécharger</span>
              </a>
            </div>
          </div>
        </div>

        <div *ngIf="!projet?.fichiers || projet.fichiers.length === 0" class="text-sm text-gray-500 italic">
          Aucun fichier joint
        </div>
        
        <!-- Boutons d'action -->
        <div class="flex flex-wrap gap-3 pt-4 border-t border-gray-100">
          <a [routerLink]="['/admin/projects/editProjet', projet?._id]" 
             class="flex-1 bg-gradient-to-r from-[#3CAEA3] to-[#20BF55] hover:from-[#2d9b91] hover:to-[#18a046] text-white py-2.5 px-4 rounded-lg font-medium text-sm text-center shadow-sm hover:shadow-md transition-all flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Modifier
          </a>
          
          <button (click)="deleteProjet(projet?._id)" 
                  class="flex-1 bg-gradient-to-r from-[#F5576C] to-[#F093FB] hover:from-[#e04054] hover:to-[#d87fe0] text-white py-2.5 px-4 rounded-lg font-medium text-sm text-center shadow-sm hover:shadow-md transition-all flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Supprimer
          </button>
          
          <a [routerLink]="['/admin/projects/rendus']" [queryParams]="{projetId: projet?._id}"
             class="flex-1 bg-gradient-to-r from-[#6C63FF] to-[#8E2DE2] hover:from-[#5046e5] hover:to-[#7816c7] text-white py-2.5 px-4 rounded-lg font-medium text-sm text-center shadow-sm hover:shadow-md transition-all flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            Voir les rendus
          </a>
        </div>
      </div>
    </div>

    <!-- Dashboard de rendu -->
    <div class="bg-white rounded-2xl shadow-md border border-[#e4e7ec] overflow-hidden">
      <div class="bg-gradient-to-r from-[#6C63FF] to-[#C77DFF] p-4 text-white">
        <h3 class="font-semibold">Statistiques de rendu</h3>
      </div>
      <div class="p-6 space-y-6">
        <!-- Progression générale -->
        <div>
          <div class="flex justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">Progression</span>
            <span class="text-sm font-medium text-[#6C63FF]">{{ (projet?.etudiantsRendus?.length || 0) }}/{{ projet?.totalEtudiants || 0 }}</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2.5">
            <div class="bg-gradient-to-r from-[#6C63FF] to-[#C77DFF] h-2.5 rounded-full" 
                 [style.width.%]="((projet?.etudiantsRendus?.length || 0) / (projet?.totalEtudiants || 1) * 100)"></div>
          </div>
        </div>

        <!-- Cartes stats -->
        <div class="grid grid-cols-2 gap-4">
          <div class="bg-[#f0f7ff] p-4 rounded-lg border border-[#e4e7ec]">
            <div class="text-2xl font-bold text-[#4A00E0]">{{ projet?.etudiantsRendus?.length || 0 }}</div>
            <div class="text-xs text-gray-500">Rendus</div>
          </div>
          <div class="bg-[#fff5f5] p-4 rounded-lg border border-[#e4e7ec]">
            <div class="text-2xl font-bold text-[#E02D6D]">{{ (projet?.totalEtudiants || 0) - (projet?.etudiantsRendus?.length || 0) }}</div>
            <div class="text-xs text-gray-500">En attente</div>
          </div>
        </div>

        <!-- Liste rapide -->
        <div>
          <h4 class="text-sm font-semibold text-gray-600 mb-3">Derniers rendus</h4>
          <div class="space-y-3">
            <div *ngFor="let etudiant of projet?.derniersRendus || []" class="flex items-center space-x-3">
              <div class="h-8 w-8 rounded-full bg-[#6C63FF] flex items-center justify-center text-white text-xs font-bold">
                {{ etudiant.nom?.charAt(0) || '' }}{{ etudiant.prenom?.charAt(0) || '' }}
              </div>
              <div class="text-sm">
                <div class="font-medium">{{ etudiant.prenom }} {{ etudiant.nom }}</div>
                <div class="text-xs text-gray-500">{{ formatDate(etudiant.dateRendu) }}</div>
              </div>
            </div>
            <div *ngIf="!projet?.derniersRendus || projet.derniersRendus.length === 0" class="text-sm text-gray-500 italic text-center py-2">
              Aucun rendu pour le moment
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>












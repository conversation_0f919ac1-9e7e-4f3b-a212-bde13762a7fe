<div class="container mx-auto px-4 py-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Mes Réunions</h1>
    <div class="flex space-x-2">
      <a routerLink="/reunions/nouvelleReunion"
         class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
        Nouvelle Réunion
      </a>
    </div>
  </div>

  <div *ngIf="loading" class="text-center py-8">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
  </div>

  <div *ngIf="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
    Erreur lors du chargement des réunions: {{ error.message }}
  </div>

  <div *ngIf="!loading && reunions.length === 0" class="text-center py-8">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
    </svg>
    <h3 class="mt-2 text-lg font-medium text-gray-900">Aucune réunion prévue</h3>
    <p class="mt-1 text-gray-500">Commencez par planifier une nouvelle réunion.</p>
  </div>

  <div *ngIf="!loading && reunions.length > 0" class="space-y-4">
    <div *ngFor="let reunion of reunions"
         class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow">
      <div class="flex justify-between items-start">
        <div>
          <h3 class="text-lg font-semibold text-gray-800">
            <a [routerLink]="['/reunions', reunion.id]" class="hover:text-purple-600">{{ reunion.titre }}</a>
          </h3>
          <p class="text-sm text-gray-600">{{ reunion.description || 'Aucune description' }}</p>
          <div class="mt-2 flex items-center text-sm text-gray-500">
            <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {{ reunion.date | date:'medium' }} - {{ reunion.heureDebut }} - {{ reunion.heureFin }}
          </div>
        </div>
        <span [class]="'px-2 py-1 text-xs rounded-full ' + getStatutClass(reunion.statut)">
          {{ reunion.statut | titlecase }}
        </span>
      </div>

      <!-- Creator Info -->
      <div class="mt-2 text-sm text-gray-600">
        <strong>Createur:</strong> {{ reunion.createur.username }} ({{ reunion.createur.email }})
      </div>

      <!-- Participants -->
      <div *ngIf="reunion.participants.length > 0" class="mt-2 text-sm text-gray-600">
        <strong>Participants:</strong>
        <ul class="list-disc pl-5">
          <li *ngFor="let participant of reunion.participants">{{ participant.username }} ({{ participant.email }})</li>
        </ul>
      </div>

      <!-- Planning Info -->
      <div class="mt-2 text-sm text-gray-600">
        <strong>Planning:</strong> {{ reunion.planning.titre }}<br />
        Date de début: {{ reunion.planning.dateDebut | date:'mediumDate' }}<br />
        Date de fin: {{ reunion.planning.dateFin | date:'mediumDate' }}
      </div>

      <!-- Lien Visio -->
      <div *ngIf="reunion.lienVisio" class="mt-2 text-sm text-gray-600">
        <strong>Lien Visio:</strong> <a href="{{ reunion.lienVisio }}" class="text-blue-600 hover:underline" target="_blank">Rejoindre la réunion</a>
      </div>

      <div class="mt-3 pt-3 border-t border-gray-100 flex justify-between items-center">
        <div class="flex items-center text-sm text-gray-500">
          <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          {{ reunion.lieu || 'Lieu non spécifié' }}
        </div>
        <a (click)="editReunion(reunion._id)"
           class="px-4 py-2 bg-blue-700 text-white rounded-md  transition-colors">
          Modifier
        </a>
      </div>
    </div>
  </div>
</div>
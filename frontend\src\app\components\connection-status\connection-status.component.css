.connection-status {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  display: flex;
  align-items: center;
  padding: 10px 20px;
  border-radius: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideDown 0.3s ease-out, fadeOut 0.3s ease-out 4.7s;
  overflow: hidden;
  backdrop-filter: blur(5px);
  position: relative;
}

.online {
  background: linear-gradient(135deg, #ff8c00, #ff6b00);
  color: white;
  box-shadow: 0 0 15px rgba(255, 140, 0, 0.5);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 15px rgba(255, 140, 0, 0.5);
  }
  50% {
    box-shadow: 0 0 25px rgba(255, 140, 0, 0.8);
  }
  100% {
    box-shadow: 0 0 15px rgba(255, 140, 0, 0.5);
  }
}

.offline {
  background-color: #f44336;
  color: white;
}

.status-content {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

.status-icon {
  margin-right: 10px;
  font-size: 18px;
}

.status-text {
  font-weight: 600;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.glow-effect {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 140, 0, 0.4) 0%,
    rgba(255, 107, 0, 0) 70%
  );
  animation: rotate 8s linear infinite;
  z-index: 1;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes slideDown {
  from {
    transform: translate(-50%, -100%);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0);
    opacity: 1;
  }
}
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

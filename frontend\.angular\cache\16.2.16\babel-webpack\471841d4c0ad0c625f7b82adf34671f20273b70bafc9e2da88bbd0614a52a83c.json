{"ast": null, "code": "import { startOfSecond } from \"./startOfSecond.js\";\n\n/**\n * @name isSameSecond\n * @category Second Helpers\n * @summary Are the given dates in the same second (and hour and day)?\n *\n * @description\n * Are the given dates in the same second (and hour and day)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n *\n * @returns The dates are in the same second (and hour and day)\n *\n * @example\n * // Are 4 September 2014 06:30:15.000 and 4 September 2014 06:30.15.500 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 30, 15),\n *   new Date(2014, 8, 4, 6, 30, 15, 500)\n * )\n * //=> true\n *\n * @example\n * // Are 4 September 2014 06:00:15.000 and 4 September 2014 06:01.15.000 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 0, 15),\n *   new Date(2014, 8, 4, 6, 1, 15)\n * )\n * //=> false\n *\n * @example\n * // Are 4 September 2014 06:00:15.000 and 5 September 2014 06:00.15.000 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 0, 15),\n *   new Date(2014, 8, 5, 6, 0, 15)\n * )\n * //=> false\n */\nexport function isSameSecond(laterDate, earlierDate) {\n  return +startOfSecond(laterDate) === +startOfSecond(earlierDate);\n}\n\n// Fallback for modularized imports:\nexport default isSameSecond;", "map": {"version": 3, "names": ["startOfSecond", "isSameSecond", "laterDate", "earlierDate"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/date-fns/isSameSecond.js"], "sourcesContent": ["import { startOfSecond } from \"./startOfSecond.js\";\n\n/**\n * @name isSameSecond\n * @category Second Helpers\n * @summary Are the given dates in the same second (and hour and day)?\n *\n * @description\n * Are the given dates in the same second (and hour and day)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n *\n * @returns The dates are in the same second (and hour and day)\n *\n * @example\n * // Are 4 September 2014 06:30:15.000 and 4 September 2014 06:30.15.500 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 30, 15),\n *   new Date(2014, 8, 4, 6, 30, 15, 500)\n * )\n * //=> true\n *\n * @example\n * // Are 4 September 2014 06:00:15.000 and 4 September 2014 06:01.15.000 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 0, 15),\n *   new Date(2014, 8, 4, 6, 1, 15)\n * )\n * //=> false\n *\n * @example\n * // Are 4 September 2014 06:00:15.000 and 5 September 2014 06:00.15.000 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 0, 15),\n *   new Date(2014, 8, 5, 6, 0, 15)\n * )\n * //=> false\n */\nexport function isSameSecond(laterDate, earlierDate) {\n  return +startOfSecond(laterDate) === +startOfSecond(earlierDate);\n}\n\n// Fallback for modularized imports:\nexport default isSameSecond;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,SAAS,EAAEC,WAAW,EAAE;EACnD,OAAO,CAACH,aAAa,CAACE,SAAS,CAAC,KAAK,CAACF,aAAa,CAACG,WAAW,CAAC;AAClE;;AAEA;AACA,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isThursday} function options.\n */\n\n/**\n * @name isThursday\n * @category Weekday Helpers\n * @summary Is the given date Thursday?\n *\n * @description\n * Is the given date Thursday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is Thursday\n *\n * @example\n * // Is 25 September 2014 Thursday?\n * const result = isThursday(new Date(2014, 8, 25))\n * //=> true\n */\nexport function isThursday(date, options) {\n  return toDate(date, options?.in).getDay() === 4;\n}\n\n// Fallback for modularized imports:\nexport default isThursday;", "map": {"version": 3, "names": ["toDate", "isThursday", "date", "options", "in", "getDay"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/date-fns/isThursday.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isThursday} function options.\n */\n\n/**\n * @name isThursday\n * @category Weekday Helpers\n * @summary Is the given date Thursday?\n *\n * @description\n * Is the given date Thursday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is Thursday\n *\n * @example\n * // Is 25 September 2014 Thursday?\n * const result = isThursday(new Date(2014, 8, 25))\n * //=> true\n */\nexport function isThursday(date, options) {\n  return toDate(date, options?.in).getDay() === 4;\n}\n\n// Fallback for modularized imports:\nexport default isThursday;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACxC,OAAOH,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,KAAK,CAAC;AACjD;;AAEA;AACA,eAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
<div class="container mx-auto px-4 py-6">
  <h1 class="text-2xl font-bold mb-6">Liste des évaluations</h1>

  <!-- Loader -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-10">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
    <div class="flex justify-between items-center">
      <div>{{ error }}</div>
      <button (click)="loadEvaluations()" 
        class="bg-red-200 hover:bg-red-300 text-red-800 font-bold py-1 px-4 rounded focus:outline-none focus:shadow-outline">
        Réessayer
      </button>
    </div>
  </div>

  <!-- Filtres -->
  <div *ngIf="!isLoading && !error" class="bg-white rounded-lg shadow-md p-4 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <!-- Recherche -->
      <div>
        <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Recherche</label>
        <input type="text" id="search" [(ngModel)]="searchTerm" (input)="onSearchChange()"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="Nom, prénom, projet...">
      </div>

      <!-- Filtre par groupe -->
      <div>
        <label for="groupe" class="block text-sm font-medium text-gray-700 mb-1">Groupe</label>
        <select id="groupe" [(ngModel)]="filterGroupe" (change)="applyFilters()"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
          <option value="">Tous les groupes</option>
          <option *ngFor="let groupe of groupes" [value]="groupe">{{ groupe }}</option>
        </select>
      </div>

      <!-- Filtre par projet -->
      <div>
        <label for="projet" class="block text-sm font-medium text-gray-700 mb-1">Projet</label>
        <select id="projet" [(ngModel)]="filterProjet" (change)="applyFilters()"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
          <option value="">Tous les projets</option>
          <option *ngFor="let projet of projets" [value]="projet._id">{{ projet.titre }}</option>
        </select>
      </div>

      <!-- Bouton de réinitialisation -->
      <div class="flex items-end">
        <button (click)="resetFilters()" 
          class="w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
          Réinitialiser les filtres
        </button>
      </div>
    </div>
  </div>

  <!-- Ajouter ce bouton dans la section des filtres -->
  <div *ngIf="!isLoading && !error" class="flex justify-end mb-4">
    <button (click)="updateMissingGroups()" 
      class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
      Mettre à jour les groupes manquants
    </button>
  </div>

  <!-- Tableau des évaluations -->
  <div *ngIf="!isLoading && !error && filteredEvaluations.length > 0" class="bg-white shadow-md rounded-lg overflow-hidden">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Étudiant
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Groupe
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Projet
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Date d'évaluation
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Score
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Actions
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <tr *ngFor="let evaluation of filteredEvaluations">
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex items-center">
              <div>
                <div class="text-sm font-medium text-gray-900">
                  {{ evaluation.etudiant?.nom || 'N/A' }} {{ evaluation.etudiant?.prenom || '' }}
                </div>
                <div class="text-sm text-gray-500">
                  {{ evaluation.etudiant?.email || 'Email non disponible' }}
                </div>
              </div>
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">{{ evaluation.etudiant?.groupe || 'Non spécifié' }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">
              {{ evaluation.projetDetails?.titre || 
                 evaluation.renduDetails?.projet?.titre || 
                 'Projet inconnu' }}
            </div>
            <div *ngIf="!evaluation.projetDetails?.titre && !evaluation.renduDetails?.projet?.titre" 
                 class="text-xs text-red-500">
              ID: {{ evaluation.projet || 'Non disponible' }}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">{{ formatDate(evaluation.dateEvaluation) }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span [ngClass]="getScoreClass(getScoreTotal(evaluation))" class="px-2 py-1 rounded-full text-sm font-medium">
              {{ getScoreTotal(evaluation) }}/20
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
            <div class="flex space-x-2">
              <button (click)="viewEvaluationDetails(evaluation.rendu)" 
                class="text-indigo-600 hover:text-indigo-900">
                Voir
              </button>
              <button (click)="editEvaluation(evaluation.rendu)" 
                class="text-green-600 hover:text-green-900">
                Modifier
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Message si aucune évaluation trouvée -->
  <div *ngIf="!isLoading && !error && filteredEvaluations.length === 0" class="bg-white shadow-md rounded-lg p-6 text-center">
    <p class="text-gray-500">Aucune évaluation trouvée</p>
  </div>
</div>




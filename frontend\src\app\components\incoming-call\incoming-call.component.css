.incoming-call-container {
  position: fixed;
  top: 30px;
  right: 30px;
  z-index: 1000;
  animation: slideIn 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  filter: drop-shadow(0 10px 25px rgba(0, 0, 0, 0.3));
}

@keyframes slideIn {
  from {
    transform: translateY(-50px) scale(0.95);
    opacity: 0;
    filter: blur(10px);
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
    filter: blur(0);
  }
}

.incoming-call-card {
  background-color: var(--dark-bg);
  border-radius: var(--border-radius-lg);
  padding: 25px;
  width: 350px;
  max-width: 90vw;
  border: 1px solid rgba(0, 247, 255, 0.2);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.incoming-call-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(
    to bottom,
    var(--accent-color),
    var(--secondary-color)
  );
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);
}

.incoming-call-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, var(--accent-color), transparent);
  opacity: 0.5;
}

.caller-info {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  position: relative;
}

.caller-avatar {
  margin-right: 20px;
  position: relative;
}

.caller-avatar::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70px;
  height: 70px;
  background: radial-gradient(circle, var(--accent-color) 0%, transparent 70%);
  opacity: 0.3;
  filter: blur(10px);
  animation: pulse 2s infinite;
  z-index: -1;
}

.avatar-img {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--accent-color);
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.3);
  position: relative;
  z-index: 1;
}

.caller-details {
  flex: 1;
}

.caller-name {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  background: linear-gradient(
    135deg,
    var(--accent-color),
    var(--secondary-color)
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.call-type {
  margin: 0;
  font-size: 15px;
  color: var(--text-dim);
  display: flex;
  align-items: center;
}

.call-type i {
  margin-right: 8px;
  color: var(--accent-color);
  animation: pulse 2s infinite;
}

.call-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
}

.call-actions button {
  flex: 1;
  padding: 15px 10px;
  border: none;
  border-radius: var(--border-radius-md);
  font-weight: 600;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
}

.call-actions button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.call-actions button:hover::before {
  opacity: 1;
}

.call-actions button i {
  font-size: 24px;
  margin-bottom: 8px;
  position: relative;
  z-index: 2;
}

.call-actions button span {
  position: relative;
  z-index: 2;
}

.reject-btn {
  background-color: rgba(255, 53, 71, 0.1);
  color: #ff3547;
  margin-right: 15px;
  border: 1px solid rgba(255, 53, 71, 0.3);
}

.reject-btn:hover {
  background: linear-gradient(135deg, #ff3547, #ff5252);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 0 15px rgba(255, 53, 71, 0.3);
  border: none;
}

.accept-btn {
  background: linear-gradient(
    135deg,
    var(--accent-color),
    var(--secondary-color)
  );
  color: white;
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.3);
}

.accept-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 0 20px rgba(0, 247, 255, 0.5);
}

@keyframes pulse {
  0% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.7;
    transform: scale(1);
  }
}

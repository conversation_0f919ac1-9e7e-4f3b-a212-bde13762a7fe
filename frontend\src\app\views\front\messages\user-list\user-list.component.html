<div
  class="flex flex-col h-full futuristic-users-container"
  [class.dark]="isDarkMode$ | async"
>
  <!-- Background decorative elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <!-- Gradient orbs -->
    <div
      class="absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/10 dark:to-transparent blur-3xl"
    ></div>
    <div
      class="absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/10 dark:to-transparent blur-3xl"
    ></div>

    <!-- Additional glow effects for dark mode -->
    <div
      class="absolute top-[40%] right-[30%] w-40 h-40 rounded-full bg-gradient-to-br from-transparent to-transparent dark:from-[#00f7ff]/5 dark:to-transparent blur-3xl opacity-0 dark:opacity-100"
    ></div>
    <div
      class="absolute bottom-[60%] left-[25%] w-32 h-32 rounded-full bg-gradient-to-tl from-transparent to-transparent dark:from-[#00f7ff]/5 dark:to-transparent blur-3xl opacity-0 dark:opacity-100"
    ></div>

    <!-- Grid pattern for light mode -->
    <div class="absolute inset-0 opacity-5 dark:opacity-0">
      <div class="h-full grid grid-cols-12">
        <div class="border-r border-[#4f5fad]"></div>
        <div class="border-r border-[#4f5fad]"></div>
        <div class="border-r border-[#4f5fad]"></div>
        <div class="border-r border-[#4f5fad]"></div>
        <div class="border-r border-[#4f5fad]"></div>
        <div class="border-r border-[#4f5fad]"></div>
        <div class="border-r border-[#4f5fad]"></div>
        <div class="border-r border-[#4f5fad]"></div>
        <div class="border-r border-[#4f5fad]"></div>
        <div class="border-r border-[#4f5fad]"></div>
        <div class="border-r border-[#4f5fad]"></div>
      </div>
    </div>

    <!-- Horizontal scan line effect for dark mode -->
    <div class="absolute inset-0 opacity-0 dark:opacity-100 overflow-hidden">
      <div class="h-px w-full bg-[#00f7ff]/20 absolute animate-scan"></div>
    </div>
  </div>
  <!-- En-tête -->
  <div class="futuristic-users-header">
    <div class="flex justify-between items-center mb-4">
      <h1 class="futuristic-title">Nouvelle Conversation</h1>
      <div class="flex space-x-2">
        <button
          (click)="refreshUsers()"
          class="futuristic-action-button"
          title="Rafraîchir la liste"
        >
          <i class="fas fa-sync-alt"></i>
        </button>
        <button
          (click)="goBackToConversations()"
          class="futuristic-action-button"
        >
          <i class="fas fa-arrow-left"></i>
        </button>
      </div>
    </div>

    <!-- Recherche et filtres -->
    <div class="space-y-3">
      <!-- Recherche -->
      <div class="relative">
        <input
          [ngModel]="searchQuery"
          (ngModelChange)="searchQuery = $event"
          type="text"
          placeholder="Rechercher des utilisateurs..."
          class="w-full pl-10 pr-4 py-2 rounded-lg futuristic-input-field"
        />
        <i
          class="fas fa-search absolute left-3 top-3 text-[#6d6870] dark:text-[#a0a0a0]"
        ></i>
      </div>

      <!-- Filtres -->
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <!-- Filtre en ligne -->
          <div class="flex items-center space-x-2">
            <label class="futuristic-checkbox-container">
              <input
                type="checkbox"
                id="onlineFilter"
                class="futuristic-checkbox"
                [checked]="filterForm.get('isOnline')?.value === true"
                (change)="
                  filterForm
                    .get('isOnline')
                    ?.setValue($any($event.target).checked ? true : null)
                "
              />
              <span class="futuristic-checkbox-checkmark"></span>
            </label>
            <label for="onlineFilter" class="futuristic-label"
              >En ligne uniquement</label
            >
          </div>

          <!-- Options de tri -->
          <div class="flex items-center space-x-2">
            <span class="futuristic-label">Trier par:</span>
            <select
              (change)="changeSortOrder($any($event.target).value)"
              class="futuristic-select"
            >
              <option [selected]="sortBy === 'username'" value="username">
                Nom
              </option>
              <option [selected]="sortBy === 'email'" value="email">
                Email
              </option>
              <option [selected]="sortBy === 'lastActive'" value="lastActive">
                Dernière activité
              </option>
            </select>
            <button
              (click)="
                sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
                loadUsers(true)
              "
              class="futuristic-sort-button"
              [title]="
                sortOrder === 'asc' ? 'Ordre croissant' : 'Ordre décroissant'
              "
            >
              <i
                [class]="
                  sortOrder === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'
                "
              ></i>
            </button>
          </div>
        </div>

        <!-- Effacer les filtres -->
        <button (click)="clearFilters()" class="futuristic-clear-button">
          Effacer les filtres
        </button>
      </div>

      <!-- Info pagination -->
      <div
        *ngIf="totalUsers > 0"
        class="flex justify-between items-center futuristic-pagination-info"
      >
        <span
          >Affichage de {{ users.length }} sur
          {{ totalUsers }} utilisateurs</span
        >
        <span>Page {{ currentPage }} sur {{ totalPages }}</span>
      </div>
    </div>
  </div>

  <!-- Liste des utilisateurs -->
  <div
    class="futuristic-users-list"
    (scroll)="
      $any($event.target).scrollTop + $any($event.target).clientHeight >=
        $any($event.target).scrollHeight - 200 && loadNextPage()
    "
  >
    <!-- État de chargement -->
    <div *ngIf="loading && !users.length" class="futuristic-loading-container">
      <div class="futuristic-loading-circle"></div>
      <div class="futuristic-loading-text">Chargement des utilisateurs...</div>
    </div>

    <!-- État vide -->
    <div *ngIf="!loading && users.length === 0" class="futuristic-empty-state">
      <div class="futuristic-empty-icon">
        <i class="fas fa-users"></i>
      </div>
      <h3 class="futuristic-empty-title">Aucun utilisateur trouvé</h3>
      <p class="futuristic-empty-text">
        Essayez un autre terme de recherche ou effacez les filtres
      </p>
    </div>

    <!-- Liste des utilisateurs -->
    <ul *ngIf="users.length > 0" class="futuristic-users-grid">
      <li *ngFor="let user of users" class="futuristic-user-card">
        <div
          class="futuristic-user-content"
          (click)="startConversation(user.id || user._id)"
        >
          <div class="futuristic-avatar">
            <img
              [src]="user.image || 'assets/images/default-avatar.png'"
              alt="User avatar"
            />
            <span
              *ngIf="user.isOnline"
              class="futuristic-online-indicator"
            ></span>
          </div>
          <div class="futuristic-user-info">
            <h3 class="futuristic-username">
              {{ user.username }}
            </h3>
            <p class="futuristic-user-email">{{ user.email }}</p>
          </div>
        </div>

        <!-- Boutons d'appel -->
        <div class="futuristic-call-buttons">
          <button
            *ngIf="user.isOnline"
            (click)="startAudioCall(user.id || user._id)"
            class="futuristic-call-button"
            title="Appel audio"
          >
            <i class="fas fa-phone"></i>
          </button>
          <button
            *ngIf="user.isOnline"
            (click)="startVideoCall(user.id || user._id)"
            class="futuristic-call-button"
            title="Appel vidéo"
          >
            <i class="fas fa-video"></i>
          </button>
        </div>
      </li>
    </ul>

    <!-- Indicateur de chargement supplémentaire -->
    <div *ngIf="loading && users.length > 0" class="futuristic-loading-more">
      <div class="futuristic-loading-dots">
        <div class="futuristic-loading-dot" style="animation-delay: 0s"></div>
        <div class="futuristic-loading-dot" style="animation-delay: 0.2s"></div>
        <div class="futuristic-loading-dot" style="animation-delay: 0.4s"></div>
      </div>
      <div class="futuristic-loading-text">
        Chargement de plus d'utilisateurs...
      </div>
    </div>

    <!-- Bouton de chargement supplémentaire -->
    <div *ngIf="hasNextPage && !loading" class="futuristic-load-more-container">
      <button (click)="loadNextPage()" class="futuristic-load-more-button">
        <i class="fas fa-chevron-down mr-2"></i>
        Charger plus d'utilisateurs
      </button>
    </div>
  </div>
</div>

{"ast": null, "code": "/**\n * Prints a string as a GraphQL StringValue literal. Replaces control characters\n * and excluded characters (\" U+0022 and \\\\ U+005C) with escape sequences.\n */\nexport function printString(str) {\n  return `\"${str.replace(escapedRegExp, escapedReplacer)}\"`;\n} // eslint-disable-next-line no-control-regex\n\nconst escapedRegExp = /[\\x00-\\x1f\\x22\\x5c\\x7f-\\x9f]/g;\nfunction escapedReplacer(str) {\n  return escapeSequences[str.charCodeAt(0)];\n} // prettier-ignore\n\nconst escapeSequences = ['\\\\u0000', '\\\\u0001', '\\\\u0002', '\\\\u0003', '\\\\u0004', '\\\\u0005', '\\\\u0006', '\\\\u0007', '\\\\b', '\\\\t', '\\\\n', '\\\\u000B', '\\\\f', '\\\\r', '\\\\u000E', '\\\\u000F', '\\\\u0010', '\\\\u0011', '\\\\u0012', '\\\\u0013', '\\\\u0014', '\\\\u0015', '\\\\u0016', '\\\\u0017', '\\\\u0018', '\\\\u0019', '\\\\u001A', '\\\\u001B', '\\\\u001C', '\\\\u001D', '\\\\u001E', '\\\\u001F', '', '', '\\\\\"', '', '', '', '', '', '', '', '', '', '', '', '', '',\n// 2F\n'', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',\n// 3F\n'', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',\n// 4F\n'', '', '', '', '', '', '', '', '', '', '', '', '\\\\\\\\', '', '', '',\n// 5F\n'', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',\n// 6F\n'', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '\\\\u007F', '\\\\u0080', '\\\\u0081', '\\\\u0082', '\\\\u0083', '\\\\u0084', '\\\\u0085', '\\\\u0086', '\\\\u0087', '\\\\u0088', '\\\\u0089', '\\\\u008A', '\\\\u008B', '\\\\u008C', '\\\\u008D', '\\\\u008E', '\\\\u008F', '\\\\u0090', '\\\\u0091', '\\\\u0092', '\\\\u0093', '\\\\u0094', '\\\\u0095', '\\\\u0096', '\\\\u0097', '\\\\u0098', '\\\\u0099', '\\\\u009A', '\\\\u009B', '\\\\u009C', '\\\\u009D', '\\\\u009E', '\\\\u009F'];", "map": {"version": 3, "names": ["printString", "str", "replace", "escapedRegExp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "escapeSequences", "charCodeAt"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/language/printString.mjs"], "sourcesContent": ["/**\n * Prints a string as a GraphQL StringValue literal. Replaces control characters\n * and excluded characters (\" U+0022 and \\\\ U+005C) with escape sequences.\n */\nexport function printString(str) {\n  return `\"${str.replace(escapedRegExp, escapedReplacer)}\"`;\n} // eslint-disable-next-line no-control-regex\n\nconst escapedRegExp = /[\\x00-\\x1f\\x22\\x5c\\x7f-\\x9f]/g;\n\nfunction escapedReplacer(str) {\n  return escapeSequences[str.charCodeAt(0)];\n} // prettier-ignore\n\nconst escapeSequences = [\n  '\\\\u0000',\n  '\\\\u0001',\n  '\\\\u0002',\n  '\\\\u0003',\n  '\\\\u0004',\n  '\\\\u0005',\n  '\\\\u0006',\n  '\\\\u0007',\n  '\\\\b',\n  '\\\\t',\n  '\\\\n',\n  '\\\\u000B',\n  '\\\\f',\n  '\\\\r',\n  '\\\\u000E',\n  '\\\\u000F',\n  '\\\\u0010',\n  '\\\\u0011',\n  '\\\\u0012',\n  '\\\\u0013',\n  '\\\\u0014',\n  '\\\\u0015',\n  '\\\\u0016',\n  '\\\\u0017',\n  '\\\\u0018',\n  '\\\\u0019',\n  '\\\\u001A',\n  '\\\\u001B',\n  '\\\\u001C',\n  '\\\\u001D',\n  '\\\\u001E',\n  '\\\\u001F',\n  '',\n  '',\n  '\\\\\"',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '', // 2F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '', // 3F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '', // 4F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '\\\\\\\\',\n  '',\n  '',\n  '', // 5F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '', // 6F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '\\\\u007F',\n  '\\\\u0080',\n  '\\\\u0081',\n  '\\\\u0082',\n  '\\\\u0083',\n  '\\\\u0084',\n  '\\\\u0085',\n  '\\\\u0086',\n  '\\\\u0087',\n  '\\\\u0088',\n  '\\\\u0089',\n  '\\\\u008A',\n  '\\\\u008B',\n  '\\\\u008C',\n  '\\\\u008D',\n  '\\\\u008E',\n  '\\\\u008F',\n  '\\\\u0090',\n  '\\\\u0091',\n  '\\\\u0092',\n  '\\\\u0093',\n  '\\\\u0094',\n  '\\\\u0095',\n  '\\\\u0096',\n  '\\\\u0097',\n  '\\\\u0098',\n  '\\\\u0099',\n  '\\\\u009A',\n  '\\\\u009B',\n  '\\\\u009C',\n  '\\\\u009D',\n  '\\\\u009E',\n  '\\\\u009F',\n];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,WAAWA,CAACC,GAAG,EAAE;EAC/B,OAAQ,IAAGA,GAAG,CAACC,OAAO,CAACC,aAAa,EAAEC,eAAe,CAAE,GAAE;AAC3D,CAAC,CAAC;;AAEF,MAAMD,aAAa,GAAG,+BAA+B;AAErD,SAASC,eAAeA,CAACH,GAAG,EAAE;EAC5B,OAAOI,eAAe,CAACJ,GAAG,CAACK,UAAU,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC;;AAEF,MAAMD,eAAe,GAAG,CACtB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS,EACT,KAAK,EACL,KAAK,EACL,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,EAAE,EACF,EAAE,EACF,KAAK,EACL,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE;AAAE;AACJ,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE;AAAE;AACJ,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE;AAAE;AACJ,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,MAAM,EACN,EAAE,EACF,EAAE,EACF,EAAE;AAAE;AACJ,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE;AAAE;AACJ,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
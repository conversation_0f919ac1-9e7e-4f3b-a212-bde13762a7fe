<div class="min-h-screen bg-[#edf1f4] flex items-center justify-center p-4">
  <div class="w-full max-w-md bg-white rounded-2xl shadow-lg overflow-hidden">
    <!-- Header -->
    <div class="border-t-4 border-[#4f5fad] p-6 text-center">
      <h1 class="text-2xl font-bold text-[#4f5fad]">
        Welcome Back to DevBridge
      </h1>
      <p class="text-[#6d6870] mt-2">
        Connectez-vous pour accéder à votre espace de travail
      </p>
    </div>

    <!-- Form -->
    <div class="p-8">
      <form #f="ngForm" (ngSubmit)="login(f)" class="space-y-6">
        <!-- Email -->
        <div>
          <label
            for="email"
            class="block text-sm font-medium text-[#6d6870] mb-1"
            >Email</label
          >
          <input
            id="email"
            type="email"
            name="email"
            required
            #email="ngModel"
            ngModel
            class="w-full px-4 py-3 rounded-lg border border-[#bdc6cc] focus:border-[#4f5fad] focus:ring-1 focus:ring-[#4f5fad]/20 transition-all"
            placeholder="<EMAIL>"
          />
          <div
            *ngIf="email.invalid && (email.dirty || email.touched)"
            class="text-[#ff6b69] text-xs mt-1"
          >
            Email est requis
          </div>
        </div>

        <!-- Password -->
        <div>
          <label
            for="password"
            class="block text-sm font-medium text-[#6d6870] mb-1"
            >Mot de passe</label
          >
          <input
            id="password"
            type="password"
            name="password"
            required
            #password="ngModel"
            ngModel
            class="w-full px-4 py-3 rounded-lg border border-[#bdc6cc] focus:border-[#4f5fad] focus:ring-1 focus:ring-[#4f5fad]/20 transition-all"
            placeholder="••••••••"
          />
          <div
            *ngIf="password.invalid && (password.dirty || password.touched)"
            class="text-[#ff6b69] text-xs mt-1"
          >
            Mot de passe est requis
          </div>
        </div>

        <!-- Error Message -->
        <div
          *ngIf="messageError"
          class="bg-[#ff6b69]/10 border border-[#ff6b69] text-[#ff6b69] p-3 rounded-lg text-sm"
        >
          {{ messageError }}
        </div>

        <!-- Submit Button -->
        <button
          type="submit"
          class="w-full bg-[#4f5fad] hover:bg-[#3d4a85] text-white font-bold py-3 px-4 rounded-lg transition-all focus:outline-none focus:ring-1 focus:ring-[#4f5fad]/20 focus:ring-offset-2"
        >
          Se connecter
        </button>

        <!-- Footer Links -->
        <div class="text-center text-sm text-[#6d6870]">
          Pas encore de compte ?
          <a
            routerLink="/registeruser"
            class="text-[#4f5fad] hover:underline font-medium"
            >S'inscrire</a
          >
        </div>
      </form>
    </div>
  </div>
</div>

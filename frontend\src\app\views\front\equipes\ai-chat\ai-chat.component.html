<div class="ai-chat-container w-100">
  <div class="card border-0 shadow-sm w-100">
    <!-- Suppression de l'en-tête redondant car il est déjà présent dans le composant parent -->
    <div class="card-body p-0">
      <!-- Messages du chat améliorés -->
      <div class="chat-messages p-3" #chatContainer>
        <div *ngFor="let message of messages; let i = index"
             class="message mb-3"
             [ngClass]="{'user-message': message.role === 'user', 'assistant-message': message.role === 'assistant'}">
          <!-- Avatar et contenu du message -->
          <div class="d-flex" [ngClass]="{'flex-row-reverse': message.role === 'user'}">
            <!-- Avatar -->
            <div class="message-avatar rounded-circle d-flex align-items-center justify-content-center me-2"
                 [ngClass]="{'bg-primary': message.role === 'user', 'bg-success': message.role === 'assistant'}">
              <i class="bi" [ngClass]="message.role === 'user' ? 'bi-person-fill' : 'bi-robot'"></i>
            </div>

            <!-- Contenu du message -->
            <div class="message-bubble p-3 rounded-4 shadow-sm"
                 [ngClass]="{'user-bubble': message.role === 'user', 'assistant-bubble': message.role === 'assistant'}">
              <p class="mb-0" [innerHTML]="message.content"></p>

              <!-- Horodatage -->
              <div class="message-time small text-muted mt-1 text-end">
                {{ message.role === 'user' ? 'Vous' : 'Assistant IA' }} • {{ getCurrentTime() }}
              </div>
            </div>
          </div>
        </div>

        <!-- Indicateur de chargement amélioré -->
        <div *ngIf="isGenerating || isAskingQuestion" class="message assistant-message mb-3">
          <div class="d-flex">
            <!-- Avatar -->
            <div class="message-avatar rounded-circle d-flex align-items-center justify-content-center me-2 bg-success">
              <i class="bi bi-robot"></i>
            </div>

            <!-- Indicateur de chargement -->
            <div class="message-bubble assistant-bubble p-3 rounded-4 shadow-sm">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Résultats générés avec en-tête amélioré -->
      <div *ngIf="generatedContent" class="generated-content p-4 border-top">
        <div class="generated-header mb-4 p-3 rounded-4 shadow-sm"
             style="background: linear-gradient(120deg, rgba(13, 110, 253, 0.1), rgba(102, 16, 242, 0.1))">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h5 class="text-primary mb-1">
                <i class="bi bi-diagram-3-fill me-2"></i>
                Plan du projet "{{ generatedContent.projectTitle }}"
              </h5>
              <p class="text-muted mb-0">
                <i class="bi bi-info-circle me-1"></i>
                {{ generatedContent.entities.length }} modules générés avec {{ countTasks(generatedContent) }} tâches au total
              </p>
            </div>
            <span class="badge bg-primary rounded-pill px-3 py-2">
              <i class="bi bi-people-fill me-1"></i>
              {{ team && team.members ? team.members.length : 0 }} membres
            </span>
          </div>
        </div>

        <!-- Affichage des modules avec une présentation plus claire et adaptée à la largeur complète -->
        <div class="row mb-4">
          <div *ngFor="let entity of generatedContent.entities; let i = index" class="col-lg-3 col-md-4 col-sm-6 mb-4">
            <div class="module-card card h-100 border-0 shadow-sm">
              <!-- Ruban indiquant le numéro du module -->
              <div class="module-ribbon" [ngStyle]="{'background': getGradientForIndex(i)}">
                <span>Module {{ i + 1 }}</span>
              </div>

              <!-- En-tête avec couleur dynamique basée sur l'index -->
              <div class="card-header text-white position-relative py-4" [ngStyle]="{'background': getGradientForIndex(i)}">
                <div class="module-icon-large rounded-circle bg-white d-flex align-items-center justify-content-center shadow">
                  <i class="bi" [ngClass]="getIconForModule(entity.name)" [ngStyle]="{'color': getColorForIndex(i)}"></i>
                </div>
                <h5 class="mt-3 mb-0 text-center">{{ entity.name }}</h5>
              </div>

              <div class="card-body">
                <!-- Description -->
                <div class="description-box p-3 rounded-3 bg-light mb-3">
                  <p class="mb-0">{{ entity.description }}</p>
                </div>

                <!-- Assignation avec style amélioré -->
                <div *ngIf="entity.assignedTo" class="assignation-badge mb-3 p-3 rounded-3 d-flex align-items-center"
                     [ngStyle]="{'background': 'linear-gradient(45deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7))',
                                'border-left': '4px solid ' + getColorForIndex(i)}">
                  <div class="member-avatar rounded-circle me-3 d-flex align-items-center justify-content-center text-white"
                       [ngStyle]="{'background': getGradientForIndex(i)}">
                    <i class="bi bi-person-fill"></i>
                  </div>
                  <div>
                    <div class="small text-muted">Responsable</div>
                    <div class="fw-bold" [ngStyle]="{'color': getColorForIndex(i)}">{{ entity.assignedTo }}</div>
                  </div>
                </div>

                <!-- En-tête de la liste des tâches -->
                <div class="task-header d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">
                  <h6 class="mb-0 d-flex align-items-center">
                    <i class="bi bi-list-check me-2" [ngStyle]="{'color': getColorForIndex(i)}"></i>
                    Tâches à réaliser
                  </h6>
                  <span class="badge rounded-pill" [ngStyle]="{'background': getGradientForIndex(i)}">
                    {{ entity.tasks.length }} tâches
                  </span>
                </div>

                <!-- Liste des tâches avec design amélioré -->
                <div class="task-list">
                  <div *ngFor="let task of entity.tasks; let j = index"
                       class="task-item mb-3 p-3 rounded-3 shadow-sm"
                       [ngClass]="{'high-priority': task.priority === 'high',
                                  'medium-priority': task.priority === 'medium',
                                  'low-priority': task.priority === 'low'}">
                    <!-- Titre et priorité -->
                    <div class="d-flex justify-content-between align-items-center mb-2">
                      <h6 class="mb-0 task-title">{{ task.title }}</h6>
                      <span class="badge rounded-pill" [ngClass]="{
                        'bg-danger': task.priority === 'high',
                        'bg-warning text-dark': task.priority === 'medium',
                        'bg-info text-dark': task.priority === 'low'
                      }">
                        {{ task.priority === 'high' ? 'Haute' :
                           task.priority === 'medium' ? 'Moyenne' : 'Basse' }}
                      </span>
                    </div>

                    <!-- Description de la tâche (toujours visible) -->
                    <div class="task-description text-muted small">
                      {{ task.description }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Affichage détaillé (accordéon) -->
        <div class="accordion d-none" id="generatedTasksAccordion">
          <div *ngFor="let entity of generatedContent.entities; let i = index" class="accordion-item border-0 mb-2">
            <h2 class="accordion-header" [id]="'heading' + i">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                      [attr.data-bs-target]="'#collapse' + i" [attr.aria-expanded]="i === 0" [attr.aria-controls]="'collapse' + i">
                <strong>{{ entity.name }}</strong>
                <span class="badge bg-primary rounded-pill ms-2">{{ entity.tasks.length }} tâches</span>
              </button>
            </h2>
            <div [id]="'collapse' + i" class="accordion-collapse collapse" [attr.aria-labelledby]="'heading' + i" data-bs-parent="#generatedTasksAccordion">
              <div class="accordion-body">
                <p class="text-muted mb-3">{{ entity.description }}</p>

                <div class="list-group">
                  <div *ngFor="let task of entity.tasks" class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                      <h6 class="mb-1">{{ task.title }}</h6>
                      <span class="badge" [ngClass]="{
                        'bg-danger': task.priority === 'high',
                        'bg-warning text-dark': task.priority === 'medium',
                        'bg-info text-dark': task.priority === 'low'
                      }">
                        {{ task.priority === 'high' ? 'Haute' :
                           task.priority === 'medium' ? 'Moyenne' : 'Basse' }}
                      </span>
                    </div>
                    <p class="mb-1 small">{{ task.description }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Bouton de création de tâches amélioré -->
        <div class="mt-5">
          <div class="card border-0 rounded-4 shadow-sm create-tasks-card">
            <div class="card-body p-4">
              <div class="row align-items-center">
                <div class="col-lg-8">
                  <h5 class="mb-3 text-success">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    Plan de projet prêt à être implémenté
                  </h5>
                  <div class="d-flex mb-3">
                    <div class="step-circle bg-success text-white me-3">1</div>
                    <div>
                      <h6 class="mb-1">Création des tâches</h6>
                      <p class="text-muted mb-0 small">{{ countTasks(generatedContent) }} tâches seront créées dans le système</p>
                    </div>
                  </div>
                  <div class="d-flex mb-3">
                    <div class="step-circle bg-primary text-white me-3">2</div>
                    <div>
                      <h6 class="mb-1">Assignation aux membres</h6>
                      <p class="text-muted mb-0 small">Les tâches seront assignées aux {{ team && team.members ? team.members.length : 0 }} membres de l'équipe</p>
                    </div>
                  </div>
                  <div class="d-flex">
                    <div class="step-circle bg-info text-white me-3">3</div>
                    <div>
                      <h6 class="mb-1">Suivi du projet</h6>
                      <p class="text-muted mb-0 small">Vous pourrez suivre l'avancement dans le tableau de bord des tâches</p>
                    </div>
                  </div>
                </div>
                <div class="col-lg-4 text-center mt-4 mt-lg-0">
                  <button class="btn btn-success btn-lg rounded-pill px-5 py-3 shadow create-button" (click)="createTasks()">
                    <i class="bi bi-plus-circle-fill me-2"></i> Créer les tâches
                  </button>
                  <div class="text-muted small mt-2">
                    <i class="bi bi-info-circle me-1"></i>
                    Cette action est irréversible
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Message d'erreur -->
      <div *ngIf="error" class="alert alert-danger m-3">
        {{ error }}
      </div>

      <!-- Entrée utilisateur améliorée -->
      <div class="chat-input p-3 border-top">
        <div *ngIf="!generatedContent" class="mb-4">
          <div class="card border-0 bg-light rounded-4 shadow-sm mb-3">
            <div class="card-body p-3">
              <h6 class="mb-3 d-flex align-items-center">
                <i class="bi bi-stars me-2 text-primary"></i>
                Générer des tâches avec l'IA
              </h6>

              <div class="mb-3">
                <label for="projectTitle" class="form-label small text-muted">Entrez le titre de votre projet</label>
                <div class="input-group">
                  <span class="input-group-text bg-white border-0">
                    <i class="bi bi-lightbulb text-primary"></i>
                  </span>
                  <input type="text" id="projectTitle" class="form-control border-0 bg-white shadow-none"
                         placeholder="Ex: Site e-commerce, Application mobile, Système de gestion..."
                         [(ngModel)]="projectTitle" [disabled]="isGenerating">
                </div>
                <small class="text-muted mt-1 d-block">
                  <i class="bi bi-info-circle me-1"></i>
                  L'IA générera {{ team && team.members ? team.members.length : 3 }} modules, un pour chaque membre de l'équipe.
                </small>
              </div>

              <div class="d-grid">
                <button class="btn btn-primary rounded-3"
                        (click)="generateTasks()"
                        [disabled]="isGenerating || !projectTitle.trim()">
                  <i class="bi" [ngClass]="isGenerating ? 'bi-hourglass-split spin' : 'bi-magic'"></i>
                  {{ isGenerating ? 'Génération en cours...' : 'Générer des tâches' }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="card border-0 bg-light rounded-4 shadow-sm">
          <div class="card-body p-2">
            <div class="input-group">
              <span class="input-group-text bg-white border-0">
                <i class="bi bi-chat-dots text-primary"></i>
              </span>
              <input type="text" class="form-control border-0 bg-white shadow-none"
                     placeholder="Posez une question sur la gestion de projet..."
                     [(ngModel)]="userQuestion" (keyup.enter)="askQuestion()"
                     [disabled]="isAskingQuestion">
              <button class="btn btn-primary rounded-circle" style="width: 38px; height: 38px;"
                      (click)="askQuestion()" [disabled]="isAskingQuestion || !userQuestion.trim()">
                <i class="bi" [ngClass]="isAskingQuestion ? 'bi-hourglass-split spin' : 'bi-send-fill'"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link min} function options.\n */\n\n/**\n * @name min\n * @category Common Helpers\n * @summary Returns the earliest of the given dates.\n *\n * @description\n * Returns the earliest of the given dates.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param dates - The dates to compare\n *\n * @returns The earliest of the dates\n *\n * @example\n * // Which of these dates is the earliest?\n * const result = min([\n *   new Date(1989, 6, 10),\n *   new Date(1987, 1, 11),\n *   new Date(1995, 6, 2),\n *   new Date(1990, 0, 1)\n * ])\n * //=> Wed Feb 11 1987 00:00:00\n */\nexport function min(dates, options) {\n  let result;\n  let context = options?.in;\n  dates.forEach(date => {\n    // Use the first date object as the context function\n    if (!context && typeof date === \"object\") context = constructFrom.bind(null, date);\n    const date_ = toDate(date, context);\n    if (!result || result > date_ || isNaN(+date_)) result = date_;\n  });\n  return constructFrom(context, result || NaN);\n}\n\n// Fallback for modularized imports:\nexport default min;", "map": {"version": 3, "names": ["constructFrom", "toDate", "min", "dates", "options", "result", "context", "in", "for<PERSON>ach", "date", "bind", "date_", "isNaN", "NaN"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/date-fns/min.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link min} function options.\n */\n\n/**\n * @name min\n * @category Common Helpers\n * @summary Returns the earliest of the given dates.\n *\n * @description\n * Returns the earliest of the given dates.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param dates - The dates to compare\n *\n * @returns The earliest of the dates\n *\n * @example\n * // Which of these dates is the earliest?\n * const result = min([\n *   new Date(1989, 6, 10),\n *   new Date(1987, 1, 11),\n *   new Date(1995, 6, 2),\n *   new Date(1990, 0, 1)\n * ])\n * //=> Wed Feb 11 1987 00:00:00\n */\nexport function min(dates, options) {\n  let result;\n  let context = options?.in;\n\n  dates.forEach((date) => {\n    // Use the first date object as the context function\n    if (!context && typeof date === \"object\")\n      context = constructFrom.bind(null, date);\n\n    const date_ = toDate(date, context);\n    if (!result || result > date_ || isNaN(+date_)) result = date_;\n  });\n\n  return constructFrom(context, result || NaN);\n}\n\n// Fallback for modularized imports:\nexport default min;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,GAAGA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAClC,IAAIC,MAAM;EACV,IAAIC,OAAO,GAAGF,OAAO,EAAEG,EAAE;EAEzBJ,KAAK,CAACK,OAAO,CAAEC,IAAI,IAAK;IACtB;IACA,IAAI,CAACH,OAAO,IAAI,OAAOG,IAAI,KAAK,QAAQ,EACtCH,OAAO,GAAGN,aAAa,CAACU,IAAI,CAAC,IAAI,EAAED,IAAI,CAAC;IAE1C,MAAME,KAAK,GAAGV,MAAM,CAACQ,IAAI,EAAEH,OAAO,CAAC;IACnC,IAAI,CAACD,MAAM,IAAIA,MAAM,GAAGM,KAAK,IAAIC,KAAK,CAAC,CAACD,KAAK,CAAC,EAAEN,MAAM,GAAGM,KAAK;EAChE,CAAC,CAAC;EAEF,OAAOX,aAAa,CAACM,OAAO,EAAED,MAAM,IAAIQ,GAAG,CAAC;AAC9C;;AAEA;AACA,eAAeX,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
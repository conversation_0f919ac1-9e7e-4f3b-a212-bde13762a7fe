{"ast": null, "code": "export function invariant(condition, message) {\n  const booleanCondition = Boolean(condition);\n  if (!booleanCondition) {\n    throw new Error(message != null ? message : 'Unexpected invariant triggered.');\n  }\n}", "map": {"version": 3, "names": ["invariant", "condition", "message", "booleanCondition", "Boolean", "Error"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/jsutils/invariant.mjs"], "sourcesContent": ["export function invariant(condition, message) {\n  const booleanCondition = Boolean(condition);\n\n  if (!booleanCondition) {\n    throw new Error(\n      message != null ? message : 'Unexpected invariant triggered.',\n    );\n  }\n}\n"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,SAAS,EAAEC,OAAO,EAAE;EAC5C,MAAMC,gBAAgB,GAAGC,OAAO,CAACH,SAAS,CAAC;EAE3C,IAAI,CAACE,gBAAgB,EAAE;IACrB,MAAM,IAAIE,KAAK,CACbH,OAAO,IAAI,IAAI,GAAGA,OAAO,GAAG,iCAC9B,CAAC;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
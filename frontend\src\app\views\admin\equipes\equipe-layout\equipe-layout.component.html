<div
  class="min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden"
>
  <!-- Background decorative elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div
      class="absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl"
    ></div>
    <div
      class="absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl"
    ></div>

    <!-- Grid pattern -->
    <div class="absolute inset-0 opacity-5 dark:opacity-[0.03]">
      <div class="h-full grid grid-cols-12">
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#00f7ff]"></div>
      </div>
    </div>
  </div>

  <!-- Layout Container -->
  <div class="flex h-screen relative z-10">
    <!-- Sidebar Navigation -->
    <div
      class="w-80 bg-white dark:bg-[#1a1a1a] shadow-xl dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] border-r border-[#4f5fad]/20 dark:border-[#00f7ff]/20 flex flex-col"
    >
      <!-- Header -->
      <div class="p-6 border-b border-[#4f5fad]/20 dark:border-[#00f7ff]/20">
        <div class="flex items-center space-x-3">
          <div
            class="w-10 h-10 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] rounded-xl flex items-center justify-center"
          >
            <i class="fas fa-users text-white text-lg"></i>
          </div>
          <div>
            <h1
              class="text-xl font-bold text-[#4f5fad] dark:text-[#00f7ff] tracking-wide"
            >
              Équipes
            </h1>
            <p class="text-sm text-[#6d6870] dark:text-[#e0e0e0]">
              Gestion collaborative
            </p>
          </div>
        </div>
      </div>

      <!-- Navigation Menu -->
      <nav class="flex-1 p-4 space-y-2">
        <!-- Liste des équipes -->
        <a
          routerLink="/equipes/liste"
          routerLinkActive="active bg-gradient-to-r from-[#4f5fad]/10 to-[#7826b5]/10 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#00f7ff] font-medium"
          class="group flex items-center px-4 py-3 text-sm font-medium rounded-xl text-[#6d6870] dark:text-[#e0e0e0] hover:bg-[#4f5fad]/10 dark:hover:bg-[#00f7ff]/10 hover:text-[#4f5fad] dark:hover:text-[#00f7ff] transition-all duration-300"
        >
          <div class="relative">
            <i
              class="fas fa-list-ul w-5 h-5 mr-3 group-hover:scale-110 transition-transform"
            ></i>
            <div
              class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#00f7ff]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full"
            ></div>
          </div>
          <span class="relative">Liste des équipes</span>
        </a>

        <!-- Créer une équipe -->
        <a
          routerLink="/equipes/nouveau"
          routerLinkActive="active bg-gradient-to-r from-[#4f5fad]/10 to-[#7826b5]/10 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#00f7ff] font-medium"
          class="group flex items-center px-4 py-3 text-sm font-medium rounded-xl text-[#6d6870] dark:text-[#e0e0e0] hover:bg-[#4f5fad]/10 dark:hover:bg-[#00f7ff]/10 hover:text-[#4f5fad] dark:hover:text-[#00f7ff] transition-all duration-300"
        >
          <div class="relative">
            <i
              class="fas fa-plus-circle w-5 h-5 mr-3 group-hover:scale-110 transition-transform"
            ></i>
            <div
              class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#00f7ff]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full"
            ></div>
          </div>
          <span class="relative">Créer une équipe</span>
        </a>

        <!-- Mes équipes -->
        <a
          routerLink="/equipes/mes-equipes"
          routerLinkActive="active bg-gradient-to-r from-[#4f5fad]/10 to-[#7826b5]/10 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#00f7ff] font-medium"
          class="group flex items-center px-4 py-3 text-sm font-medium rounded-xl text-[#6d6870] dark:text-[#e0e0e0] hover:bg-[#4f5fad]/10 dark:hover:bg-[#00f7ff]/10 hover:text-[#4f5fad] dark:hover:text-[#00f7ff] transition-all duration-300"
        >
          <div class="relative">
            <i
              class="fas fa-user-friends w-5 h-5 mr-3 group-hover:scale-110 transition-transform"
            ></i>
            <div
              class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#00f7ff]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full"
            ></div>
          </div>
          <span class="relative">Mes équipes</span>
        </a>

        <!-- Divider -->
        <div
          class="my-4 border-t border-[#4f5fad]/20 dark:border-[#00f7ff]/20"
        ></div>

        <!-- Statistiques -->
        <div class="px-4 py-3">
          <h3
            class="text-xs font-semibold text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider mb-3"
          >
            Statistiques
          </h3>
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-sm text-[#6d6870] dark:text-[#e0e0e0]"
                >Équipes créées</span
              >
              <span
                class="text-sm font-medium text-[#4f5fad] dark:text-[#00f7ff]"
                >0</span
              >
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-[#6d6870] dark:text-[#e0e0e0]"
                >Membres actifs</span
              >
              <span class="text-sm font-medium text-[#00ff9d]">0</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-[#6d6870] dark:text-[#e0e0e0]"
                >Projets en cours</span
              >
              <span
                class="text-sm font-medium text-[#ff6b69] dark:text-[#ff3b30]"
                >0</span
              >
            </div>
          </div>
        </div>
      </nav>

      <!-- Footer -->
      <div class="p-4 border-t border-[#4f5fad]/20 dark:border-[#00f7ff]/20">
        <button
          onclick="history.back()"
          class="w-full bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 text-[#6d6870] dark:text-[#e0e0e0] px-4 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#6d6870]/30 dark:hover:bg-[#a0a0a0]/30 flex items-center justify-center"
        >
          <i class="fas fa-arrow-left mr-2"></i>
          Retour à l'accueil
        </button>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Top Bar -->
      <header
        class="bg-white dark:bg-[#1a1a1a] shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] border-b border-[#4f5fad]/20 dark:border-[#00f7ff]/20 px-6 py-4"
      >
        <div class="flex items-center justify-between">
          <!-- Page Title -->
          <div class="flex items-center space-x-4">
            <div
              class="w-8 h-8 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] rounded-lg flex items-center justify-center"
            >
              <i class="fas fa-users text-white text-sm"></i>
            </div>
            <div>
              <h2 class="text-xl font-bold text-[#4f5fad] dark:text-[#00f7ff]">
                Gestion des Équipes
              </h2>
              <p class="text-sm text-[#6d6870] dark:text-[#e0e0e0]">
                Organisez et gérez vos équipes de projet
              </p>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex items-center space-x-3">
            <!-- Search -->
            <div class="relative">
              <input
                type="text"
                placeholder="Rechercher..."
                class="w-64 pl-10 pr-4 py-2 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#e0e0e0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0] focus:outline-none focus:ring-2 focus:ring-[#4f5fad] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all"
              />
              <div
                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
              >
                <i class="fas fa-search text-[#6d6870] dark:text-[#a0a0a0]"></i>
              </div>
            </div>

            <!-- Quick Actions -->
            <button
              routerLink="/equipes/nouveau"
              class="bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white px-4 py-2 rounded-lg font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(79,95,173,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)] flex items-center"
            >
              <i class="fas fa-plus mr-2"></i>
              Nouvelle équipe
            </button>
          </div>
        </div>
      </header>

      <!-- Content Area -->
      <main class="flex-1 overflow-auto p-6">
        <router-outlet></router-outlet>
      </main>
    </div>
  </div>
</div>

<div class="min-h-screen bg-[#edf1f4] p-4 md:p-6">
  <div class="max-w-4xl mx-auto">
    <!-- En-tête -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
      <div class="border-t-4 border-[#4f5fad] p-6">
        <div class="flex justify-between items-start">
          <div>
            <h1 class="text-2xl font-bold text-[#4f5fad] mb-2">
              {{ reunion?.titre || "Détails de la réunion" }}
            </h1>
            <p class="text-[#6d6870]">
              {{ reunion?.description || "Chargement des détails..." }}
            </p>
          </div>
          <div class="flex space-x-2">
            <button
              *ngIf="reunion"
              routerLink="/reunions/edit/{{ reunion._id }}"
              class="p-2 rounded-full text-[#4f5fad] hover:bg-[#4f5fad]/10 transition-colors"
            >
              <i class="fas fa-edit"></i>
            </button>
            <button
              routerLink="/reunions"
              class="p-2 rounded-full text-[#4f5fad] hover:bg-[#4f5fad]/10 transition-colors"
            >
              <i class="fas fa-arrow-left"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- État de chargement -->
    <div *ngIf="loading" class="flex flex-col items-center justify-center p-12">
      <div
        class="w-12 h-12 border-4 border-[#4f5fad]/20 border-t-[#4f5fad] rounded-full animate-spin mb-4"
      ></div>
      <p class="text-[#6d6870]">Chargement des détails de la réunion...</p>
    </div>

    <!-- État d'erreur -->
    <div
      *ngIf="error"
      class="bg-[#ff6b69]/10 border border-[#ff6b69] rounded-lg p-4 mb-6"
    >
      <div class="flex items-start">
        <i class="fas fa-exclamation-triangle text-[#ff6b69] text-xl mr-3"></i>
        <div>
          <h3 class="font-medium text-[#ff6b69] mb-1">Erreur de chargement</h3>
          <p class="text-sm text-[#6d6870]">{{ error }}</p>
          <button
            (click)="loadReunion()"
            class="mt-3 px-3 py-1 bg-[#ff6b69] text-white rounded-md hover:bg-[#ff6b69]/80 transition-colors text-sm"
          >
            Réessayer
          </button>
        </div>
      </div>
    </div>

    <!-- Détails de la réunion -->
    <div
      *ngIf="reunion && !loading"
      class="grid grid-cols-1 md:grid-cols-3 gap-6"
    >
      <!-- Informations principales -->
      <div class="md:col-span-2 bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6">
          <h2 class="text-lg font-semibold text-[#4f5fad] mb-4">
            Informations
          </h2>

          <div class="space-y-4">
            <div>
              <h3 class="text-sm font-medium text-[#6d6870]">Date et heure</h3>
              <p class="text-[#6d6870]">
                {{ reunion.dateDebut | date : "dd/MM/yyyy HH:mm" }} -
                {{ reunion.dateFin | date : "HH:mm" }}
              </p>
            </div>

            <div>
              <h3 class="text-sm font-medium text-[#6d6870]">Lieu</h3>
              <p class="text-[#6d6870]">{{ reunion.lieu || "Non spécifié" }}</p>
            </div>

            <div *ngIf="reunion.lienVisio">
              <h3 class="text-sm font-medium text-[#6d6870]">
                Lien de visioconférence
              </h3>
              <a
                [href]="reunion.lienVisio"
                target="_blank"
                class="text-[#4f5fad] hover:underline flex items-center"
              >
                <i class="fas fa-video mr-2"></i>
                Rejoindre la réunion
              </a>
            </div>

            <div *ngIf="reunion.planningId">
              <h3 class="text-sm font-medium text-[#6d6870]">
                Planning associé
              </h3>
              <a
                [routerLink]="['/plannings', reunion.planningId._id]"
                class="text-[#4f5fad] hover:underline"
              >
                {{ reunion.planningId.titre }}
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Participants -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6">
          <h2 class="text-lg font-semibold text-[#4f5fad] mb-4">
            Participants
          </h2>

          <div
            *ngIf="reunion.participants && reunion.participants.length > 0"
            class="space-y-3"
          >
            <div
              *ngFor="let participant of reunion.participants"
              class="flex items-center"
            >
              <div class="relative mr-3">
                <img
                  [src]="
                    participant.image || 'assets/images/default-avatar.png'
                  "
                  alt="Avatar"
                  class="w-8 h-8 rounded-full object-cover"
                />
              </div>
              <span class="text-[#6d6870]">{{ participant.username }}</span>
            </div>
          </div>

          <div
            *ngIf="!reunion.participants || reunion.participants.length === 0"
            class="text-[#6d6870] italic"
          >
            Aucun participant
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{"ast": null, "code": "import { constructNow } from \"./constructNow.js\";\nimport { formatDistanceStrict } from \"./formatDistanceStrict.js\";\n\n/**\n * The {@link formatDistanceToNowStrict} function options.\n */\n\n/**\n * @name formatDistanceToNowStrict\n * @category Common Helpers\n * @summary Return the distance between the given date and now in words.\n * @pure false\n *\n * @description\n * Return the distance between the given dates in words, using strict units.\n * This is like `formatDistance`, but does not use helpers like 'almost', 'over',\n * 'less than' and the like.\n *\n * | Distance between dates | Result              |\n * |------------------------|---------------------|\n * | 0 ... 59 secs          | [0..59] seconds     |\n * | 1 ... 59 mins          | [1..59] minutes     |\n * | 1 ... 23 hrs           | [1..23] hours       |\n * | 1 ... 29 days          | [1..29] days        |\n * | 1 ... 11 months        | [1..11] months      |\n * | 1 ... N years          | [1..N]  years       |\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The distance in words\n *\n * @throws `date` must not be Invalid Date\n * @throws `options.locale` must contain `formatDistance` property\n *\n * @example\n * // If today is 1 January 2015, what is the distance to 2 July 2014?\n * const result = formatDistanceToNowStrict(\n *   new Date(2014, 6, 2)\n * )\n * //=> '6 months'\n *\n * @example\n * // If now is 1 January 2015 00:00:00,\n * // what is the distance to 1 January 2015 00:00:15, including seconds?\n * const result = formatDistanceToNowStrict(\n *   new Date(2015, 0, 1, 0, 0, 15)\n * )\n * //=> '15 seconds'\n *\n * @example\n * // If today is 1 January 2015,\n * // what is the distance to 1 January 2016, with a suffix?\n * const result = formatDistanceToNowStrict(\n *   new Date(2016, 0, 1),\n *   {addSuffix: true}\n * )\n * //=> 'in 1 year'\n *\n * @example\n * // If today is 28 January 2015,\n * // what is the distance to 1 January 2015, in months, rounded up??\n * const result = formatDistanceToNowStrict(new Date(2015, 0, 1), {\n *   unit: 'month',\n *   roundingMethod: 'ceil'\n * })\n * //=> '1 month'\n *\n * @example\n * // If today is 1 January 2015,\n * // what is the distance to 1 January 2016 in Esperanto?\n * const eoLocale = require('date-fns/locale/eo')\n * const result = formatDistanceToNowStrict(\n *   new Date(2016, 0, 1),\n *   {locale: eoLocale}\n * )\n * //=> '1 jaro'\n */\nexport function formatDistanceToNowStrict(date, options) {\n  return formatDistanceStrict(date, constructNow(date), options);\n}\n\n// Fallback for modularized imports:\nexport default formatDistanceToNowStrict;", "map": {"version": 3, "names": ["constructNow", "formatDistanceStrict", "formatDistanceToNowStrict", "date", "options"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/date-fns/formatDistanceToNowStrict.js"], "sourcesContent": ["import { constructNow } from \"./constructNow.js\";\n\nimport { formatDistanceStrict } from \"./formatDistanceStrict.js\";\n\n/**\n * The {@link formatDistanceToNowStrict} function options.\n */\n\n/**\n * @name formatDistanceToNowStrict\n * @category Common Helpers\n * @summary Return the distance between the given date and now in words.\n * @pure false\n *\n * @description\n * Return the distance between the given dates in words, using strict units.\n * This is like `formatDistance`, but does not use helpers like 'almost', 'over',\n * 'less than' and the like.\n *\n * | Distance between dates | Result              |\n * |------------------------|---------------------|\n * | 0 ... 59 secs          | [0..59] seconds     |\n * | 1 ... 59 mins          | [1..59] minutes     |\n * | 1 ... 23 hrs           | [1..23] hours       |\n * | 1 ... 29 days          | [1..29] days        |\n * | 1 ... 11 months        | [1..11] months      |\n * | 1 ... N years          | [1..N]  years       |\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The distance in words\n *\n * @throws `date` must not be Invalid Date\n * @throws `options.locale` must contain `formatDistance` property\n *\n * @example\n * // If today is 1 January 2015, what is the distance to 2 July 2014?\n * const result = formatDistanceToNowStrict(\n *   new Date(2014, 6, 2)\n * )\n * //=> '6 months'\n *\n * @example\n * // If now is 1 January 2015 00:00:00,\n * // what is the distance to 1 January 2015 00:00:15, including seconds?\n * const result = formatDistanceToNowStrict(\n *   new Date(2015, 0, 1, 0, 0, 15)\n * )\n * //=> '15 seconds'\n *\n * @example\n * // If today is 1 January 2015,\n * // what is the distance to 1 January 2016, with a suffix?\n * const result = formatDistanceToNowStrict(\n *   new Date(2016, 0, 1),\n *   {addSuffix: true}\n * )\n * //=> 'in 1 year'\n *\n * @example\n * // If today is 28 January 2015,\n * // what is the distance to 1 January 2015, in months, rounded up??\n * const result = formatDistanceToNowStrict(new Date(2015, 0, 1), {\n *   unit: 'month',\n *   roundingMethod: 'ceil'\n * })\n * //=> '1 month'\n *\n * @example\n * // If today is 1 January 2015,\n * // what is the distance to 1 January 2016 in Esperanto?\n * const eoLocale = require('date-fns/locale/eo')\n * const result = formatDistanceToNowStrict(\n *   new Date(2016, 0, 1),\n *   {locale: eoLocale}\n * )\n * //=> '1 jaro'\n */\nexport function formatDistanceToNowStrict(date, options) {\n  return formatDistanceStrict(date, constructNow(date), options);\n}\n\n// Fallback for modularized imports:\nexport default formatDistanceToNowStrict;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAEhD,SAASC,oBAAoB,QAAQ,2BAA2B;;AAEhE;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,yBAAyBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACvD,OAAOH,oBAAoB,CAACE,IAAI,EAAEH,YAAY,CAACG,IAAI,CAAC,EAAEC,OAAO,CAAC;AAChE;;AAEA;AACA,eAAeF,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
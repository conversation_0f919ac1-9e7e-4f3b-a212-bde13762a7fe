<!-- Begin Page Content -->
<div class="container-fluid p-4 md:p-6 bg-[#edf1f4] min-h-screen">
  <!-- Page Heading -->
  <div
    class="flex flex-col md:flex-row md:items-center md:justify-between mb-6"
  >
    <div>
      <h1 class="text-2xl font-bold text-[#4f5fad] mb-2 md:mb-0">
        Gestion des Projets
      </h1>
      <p class="text-[#6d6870] text-sm">
        <PERSON><PERSON><PERSON>, g<PERSON><PERSON> et suivez vos projets académiques
      </p>
    </div>
    <a
      routerLink="/admin/projects/new"
      class="inline-flex items-center bg-[#7826b5] hover:bg-[#5f1d8f] text-white px-4 py-2 rounded-lg shadow transition-all mt-4 md:mt-0"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4 mr-2"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z"
          clip-rule="evenodd"
        />
      </svg>
      Ajouter un projet
    </a>
  </div>

  <!-- Projects Grid -->
  <div
    *ngIf="!isLoading && projets && projets.length > 0"
    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8"
  >
    <!-- Project Card -->
    <div
      *ngFor="let projet of projets"
      class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 group"
    >
      <!-- Project Header with border -->
      <div class="border-t-4 border-[#4f5fad] p-5 relative">
        <div class="flex justify-between items-start">
          <h3 class="text-xl font-bold text-[#4f5fad] truncate pr-16">
            {{ projet.titre }}
          </h3>
          <div
            class="absolute top-4 right-4 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <!-- Edit Button -->
            <a
              [routerLink]="['/admin/projects/editProjet', projet._id]"
              class="bg-[#edf1f4] hover:bg-[#dce4ec] p-1.5 rounded-lg text-[#4f5fad] transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"
                />
              </svg>
            </a>

            <!-- Delete Button -->
            <a
              (click)="projet._id && openDeleteDialog(projet._id)"
              class="bg-[#edf1f4] hover:bg-[#dce4ec] p-1.5 rounded-lg text-[#ff6b69] transition-colors cursor-pointer"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 112 0v6a1 1 0 11-2 0V8z"
                  clip-rule="evenodd"
                />
              </svg>
            </a>
          </div>
        </div>
        <div class="flex items-center mt-3 text-sm">
          <span
            class="bg-[#4f5fad]/10 px-2.5 py-1 rounded-full text-xs font-medium text-[#4f5fad]"
            >{{ projet.groupe || "Tous" }}</span
          >
          <span class="mx-2 text-[#bdc6cc]">•</span>
          <span class="text-[#6d6870] text-xs">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-3.5 w-3.5 inline mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            {{ projet.dateLimite | date : "dd/MM/yyyy" }}
          </span>
        </div>
      </div>

      <!-- Project Content -->
      <div class="p-5 border-t border-[#edf1f4]">
        <!-- Description -->
        <div class="mb-5">
          <h4
            class="text-xs font-semibold text-[#6d6870] uppercase tracking-wider mb-2 flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1 text-[#4f5fad]"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            Description
          </h4>
          <p class="text-[#6d6870] text-sm line-clamp-3">
            {{ projet.description || "Aucune description fournie" }}
          </p>
        </div>

        <!-- Files Section -->
        <div *ngIf="projet.fichiers && projet.fichiers.length > 0" class="mb-5">
          <h4
            class="text-xs font-semibold text-[#6d6870] uppercase tracking-wider mb-2 flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1 text-[#4f5fad]"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            Fichiers ({{ projet.fichiers.length }})
          </h4>
          <div class="space-y-2 bg-[#edf1f4] rounded-lg p-3">
            <div
              *ngFor="let file of projet.fichiers"
              class="flex items-center justify-between text-sm"
            >
              <div class="flex items-center truncate max-w-[70%]">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 text-[#7826b5] mr-2 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                  />
                </svg>
                <span class="truncate text-[#6d6870]">{{
                  getFileName(file)
                }}</span>
              </div>
              <a
                [href]="getFileUrl(file)"
                [download]="getFileName(file)"
                class="text-[#4f5fad] hover:text-[#7826b5] flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                  />
                </svg>
                Télécharger
              </a>
            </div>
          </div>
        </div>

        <!-- No Files Message -->
        <div
          *ngIf="!projet.fichiers || projet.fichiers.length === 0"
          class="mb-5"
        >
          <h4
            class="text-xs font-semibold text-[#6d6870] uppercase tracking-wider mb-2 flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1 text-[#4f5fad]"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            Fichiers
          </h4>
          <div
            class="bg-[#edf1f4] rounded-lg p-3 text-sm text-[#6d6870] flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1 text-[#bdc6cc]"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            Aucun fichier joint
          </div>
        </div>

        <!-- Actions -->
        <div class="mt-6 flex space-x-3">
          <a
            [routerLink]="['/admin/projects/details', projet._id]"
            class="flex-1 bg-[#edf1f4] hover:bg-[#dce4ec] text-[#4f5fad] py-2 px-4 rounded-lg text-sm font-medium text-center flex items-center justify-center transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1.5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            Détails
          </a>
          <a
            [routerLink]="['/admin/projects/rendus']"
            [queryParams]="{ projetId: projet._id }"
            class="flex-1 bg-[#7826b5] hover:bg-[#5f1d8f] text-white py-2 px-4 rounded-lg text-sm font-medium text-center flex items-center justify-center transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1.5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg>
            Rendus
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="text-center py-16">
    <div
      class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#4f5fad] mx-auto"
    ></div>
    <p class="mt-4 text-[#6d6870] font-medium">Chargement des projets...</p>
  </div>

  <!-- Empty State -->
  <div
    *ngIf="!isLoading && (!projets || projets.length === 0)"
    class="bg-white rounded-xl shadow-md p-8 text-center mb-8"
  >
    <div
      class="w-20 h-20 mx-auto mb-6 bg-[#f0e6ff] rounded-full flex items-center justify-center"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-10 w-10 text-[#7826b5]"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
        />
      </svg>
    </div>
    <h3 class="text-xl font-bold text-[#4f5fad] mb-2">
      Aucun projet disponible
    </h3>
    <p class="text-[#6d6870] mb-6 max-w-md mx-auto">
      Commencez par créer votre premier projet en cliquant sur le bouton
      ci-dessous
    </p>
    <a
      routerLink="/admin/projects/new"
      class="inline-flex items-center px-4 py-2 bg-[#7826b5] hover:bg-[#5f1d8f] text-white rounded-lg shadow transition-all"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 mr-2"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z"
        />
      </svg>
      Ajouter un projet
    </a>
  </div>

  <!-- Confirmation Dialog -->
  <div
    *ngIf="showDeleteDialog"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div class="bg-white rounded-xl shadow-lg w-full max-w-md p-6">
      <h2 class="text-xl font-bold text-[#4f5fad] mb-4">
        {{ dialogData.title }}
      </h2>
      <p class="text-[#6d6870] mb-6">{{ dialogData.message }}</p>
      <div class="flex justify-end space-x-4">
        <button
          class="px-4 py-2 bg-[#edf1f4] text-[#4f5fad] hover:bg-[#dce4ec] rounded-lg"
          (click)="onDeleteCancel()"
        >
          Annuler
        </button>
        <button
          class="px-4 py-2 bg-[#7826b5] hover:bg-[#5f1d8f] text-white rounded-lg"
          (click)="onDeleteConfirm()"
        >
          Supprimer
        </button>
      </div>
    </div>
  </div>
</div>

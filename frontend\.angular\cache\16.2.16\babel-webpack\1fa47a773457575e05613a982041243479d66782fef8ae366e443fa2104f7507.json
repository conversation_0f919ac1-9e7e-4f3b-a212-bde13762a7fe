{"ast": null, "code": "import { Slot } from \"@wry/context\";\nexport const parentEntrySlot = new Slot();\nexport function nonReactive(fn) {\n  return parentEntrySlot.withValue(void 0, fn);\n}\nexport { Slot };\nexport { bind as bindContext, noContext, setTimeout, asyncFromGen } from \"@wry/context\";", "map": {"version": 3, "names": ["Slot", "parentEntrySlot", "nonReactive", "fn", "with<PERSON><PERSON><PERSON>", "bind", "bindContext", "noContext", "setTimeout", "asyncFromGen"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/optimism/lib/context.js"], "sourcesContent": ["import { Slot } from \"@wry/context\";\nexport const parentEntrySlot = new Slot();\nexport function nonReactive(fn) {\n    return parentEntrySlot.withValue(void 0, fn);\n}\nexport { Slot };\nexport { bind as bindContext, noContext, setTimeout, asyncFromGen, } from \"@wry/context\";\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,cAAc;AACnC,OAAO,MAAMC,eAAe,GAAG,IAAID,IAAI,CAAC,CAAC;AACzC,OAAO,SAASE,WAAWA,CAACC,EAAE,EAAE;EAC5B,OAAOF,eAAe,CAACG,SAAS,CAAC,KAAK,CAAC,EAAED,EAAE,CAAC;AAChD;AACA,SAASH,IAAI;AACb,SAASK,IAAI,IAAIC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEC,YAAY,QAAS,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
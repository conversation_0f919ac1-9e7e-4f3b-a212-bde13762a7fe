{"ast": null, "code": "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link lastDayOfWeek} function options.\n */\n\n/**\n * @name lastDayOfWeek\n * @category Week Helpers\n * @summary Return the last day of a week for the given date.\n *\n * @description\n * Return the last day of a week for the given date.\n * The result will be in the local timezone unless a context is specified.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The last day of a week\n */\nexport function lastDayOfWeek(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn = options?.weekStartsOn ?? options?.locale?.options?.weekStartsOn ?? defaultOptions.weekStartsOn ?? defaultOptions.locale?.options?.weekStartsOn ?? 0;\n  const _date = toDate(date, options?.in);\n  const day = _date.getDay();\n  const diff = (day < weekStartsOn ? -7 : 0) + 6 - (day - weekStartsOn);\n  _date.setHours(0, 0, 0, 0);\n  _date.setDate(_date.getDate() + diff);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfWeek;", "map": {"version": 3, "names": ["getDefaultOptions", "toDate", "lastDayOfWeek", "date", "options", "defaultOptions", "weekStartsOn", "locale", "_date", "in", "day", "getDay", "diff", "setHours", "setDate", "getDate"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/date-fns/lastDayOfWeek.js"], "sourcesContent": ["import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link lastDayOfWeek} function options.\n */\n\n/**\n * @name lastDayOfWeek\n * @category Week Helpers\n * @summary Return the last day of a week for the given date.\n *\n * @description\n * Return the last day of a week for the given date.\n * The result will be in the local timezone unless a context is specified.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The last day of a week\n */\nexport function lastDayOfWeek(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const _date = toDate(date, options?.in);\n  const day = _date.getDay();\n  const diff = (day < weekStartsOn ? -7 : 0) + 6 - (day - weekStartsOn);\n\n  _date.setHours(0, 0, 0, 0);\n  _date.setDate(_date.getDate() + diff);\n\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfWeek;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC3C,MAAMC,cAAc,GAAGL,iBAAiB,CAAC,CAAC;EAC1C,MAAMM,YAAY,GAChBF,OAAO,EAAEE,YAAY,IACrBF,OAAO,EAAEG,MAAM,EAAEH,OAAO,EAAEE,YAAY,IACtCD,cAAc,CAACC,YAAY,IAC3BD,cAAc,CAACE,MAAM,EAAEH,OAAO,EAAEE,YAAY,IAC5C,CAAC;EAEH,MAAME,KAAK,GAAGP,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEK,EAAE,CAAC;EACvC,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC,CAAC;EAC1B,MAAMC,IAAI,GAAG,CAACF,GAAG,GAAGJ,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAII,GAAG,GAAGJ,YAAY,CAAC;EAErEE,KAAK,CAACK,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1BL,KAAK,CAACM,OAAO,CAACN,KAAK,CAACO,OAAO,CAAC,CAAC,GAAGH,IAAI,CAAC;EAErC,OAAOJ,KAAK;AACd;;AAEA;AACA,eAAeN,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
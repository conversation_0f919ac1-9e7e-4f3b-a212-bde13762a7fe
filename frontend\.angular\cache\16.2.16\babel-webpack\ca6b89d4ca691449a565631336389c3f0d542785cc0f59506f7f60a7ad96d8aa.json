{"ast": null, "code": "/**\n * Returns true if the provided object implements the AsyncIterator protocol via\n * implementing a `Symbol.asyncIterator` method.\n */\nexport function isAsyncIterable(maybeAsyncIterable) {\n  return typeof (maybeAsyncIterable === null || maybeAsyncIterable === void 0 ? void 0 : maybeAsyncIterable[Symbol.asyncIterator]) === 'function';\n}", "map": {"version": 3, "names": ["isAsyncIterable", "maybeAsyncIterable", "Symbol", "asyncIterator"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/jsutils/isAsyncIterable.mjs"], "sourcesContent": ["/**\n * Returns true if the provided object implements the AsyncIterator protocol via\n * implementing a `Symbol.asyncIterator` method.\n */\nexport function isAsyncIterable(maybeAsyncIterable) {\n  return (\n    typeof (maybeAsyncIterable === null || maybeAsyncIterable === void 0\n      ? void 0\n      : maybeAsyncIterable[Symbol.asyncIterator]) === 'function'\n  );\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,eAAeA,CAACC,kBAAkB,EAAE;EAClD,OACE,QAAQA,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAChE,KAAK,CAAC,GACNA,kBAAkB,CAACC,MAAM,CAACC,aAAa,CAAC,CAAC,KAAK,UAAU;AAEhE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
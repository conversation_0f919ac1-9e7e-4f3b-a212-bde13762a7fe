{"ast": null, "code": "import { Parser } from \"../Parser.js\";\nexport class <PERSON><PERSON>ars<PERSON> extends Parser {\n  priority = 140;\n  parse(dateString, token, match) {\n    switch (token) {\n      // AD, BC\n      case \"G\":\n      case \"GG\":\n      case \"GGG\":\n        return match.era(dateString, {\n          width: \"abbreviated\"\n        }) || match.era(dateString, {\n          width: \"narrow\"\n        });\n\n      // A, B\n      case \"GGGGG\":\n        return match.era(dateString, {\n          width: \"narrow\"\n        });\n      // <PERSON><PERSON>, Before Christ\n      case \"GGGG\":\n      default:\n        return match.era(dateString, {\n          width: \"wide\"\n        }) || match.era(dateString, {\n          width: \"abbreviated\"\n        }) || match.era(dateString, {\n          width: \"narrow\"\n        });\n    }\n  }\n  set(date, flags, value) {\n    flags.era = value;\n    date.setFullYear(value, 0, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n  incompatibleTokens = [\"R\", \"u\", \"t\", \"T\"];\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "priority", "parse", "dateString", "token", "match", "era", "width", "set", "date", "flags", "value", "setFullYear", "setHours", "incompatibleTokens"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/date-fns/parse/_lib/parsers/EraParser.js"], "sourcesContent": ["import { Parser } from \"../Parser.js\";\n\nexport class <PERSON><PERSON>ars<PERSON> extends Parser {\n  priority = 140;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      // AD, BC\n      case \"G\":\n      case \"GG\":\n      case \"GGG\":\n        return (\n          match.era(dateString, { width: \"abbreviated\" }) ||\n          match.era(dateString, { width: \"narrow\" })\n        );\n\n      // A, B\n      case \"GGGGG\":\n        return match.era(dateString, { width: \"narrow\" });\n      // <PERSON><PERSON>, Before Christ\n      case \"GGGG\":\n      default:\n        return (\n          match.era(dateString, { width: \"wide\" }) ||\n          match.era(dateString, { width: \"abbreviated\" }) ||\n          match.era(dateString, { width: \"narrow\" })\n        );\n    }\n  }\n\n  set(date, flags, value) {\n    flags.era = value;\n    date.setFullYear(value, 0, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"R\", \"u\", \"t\", \"T\"];\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;AAErC,OAAO,MAAMC,SAAS,SAASD,MAAM,CAAC;EACpCE,QAAQ,GAAG,GAAG;EAEdC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC9B,QAAQD,KAAK;MACX;MACA,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,KAAK;QACR,OACEC,KAAK,CAACC,GAAG,CAACH,UAAU,EAAE;UAAEI,KAAK,EAAE;QAAc,CAAC,CAAC,IAC/CF,KAAK,CAACC,GAAG,CAACH,UAAU,EAAE;UAAEI,KAAK,EAAE;QAAS,CAAC,CAAC;;MAG9C;MACA,KAAK,OAAO;QACV,OAAOF,KAAK,CAACC,GAAG,CAACH,UAAU,EAAE;UAAEI,KAAK,EAAE;QAAS,CAAC,CAAC;MACnD;MACA,KAAK,MAAM;MACX;QACE,OACEF,KAAK,CAACC,GAAG,CAACH,UAAU,EAAE;UAAEI,KAAK,EAAE;QAAO,CAAC,CAAC,IACxCF,KAAK,CAACC,GAAG,CAACH,UAAU,EAAE;UAAEI,KAAK,EAAE;QAAc,CAAC,CAAC,IAC/CF,KAAK,CAACC,GAAG,CAACH,UAAU,EAAE;UAAEI,KAAK,EAAE;QAAS,CAAC,CAAC;IAEhD;EACF;EAEAC,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAE;IACtBD,KAAK,CAACJ,GAAG,GAAGK,KAAK;IACjBF,IAAI,CAACG,WAAW,CAACD,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7BF,IAAI,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzB,OAAOJ,IAAI;EACb;EAEAK,kBAAkB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
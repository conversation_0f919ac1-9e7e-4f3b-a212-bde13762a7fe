{"ast": null, "code": "import { inspect } from './inspect.mjs';\n/**\n * A replacement for instanceof which includes an error warning when multi-realm\n * constructors are detected.\n * See: https://expressjs.com/en/advanced/best-practice-performance.html#set-node_env-to-production\n * See: https://webpack.js.org/guides/production/\n */\n\nexport const instanceOf = /* c8 ignore next 6 */\n// FIXME: https://github.com/graphql/graphql-js/issues/2317\nglobalThis.process && globalThis.process.env.NODE_ENV === 'production' ? function instanceOf(value, constructor) {\n  return value instanceof constructor;\n} : function instanceOf(value, constructor) {\n  if (value instanceof constructor) {\n    return true;\n  }\n  if (typeof value === 'object' && value !== null) {\n    var _value$constructor;\n\n    // Prefer Symbol.toStringTag since it is immune to minification.\n    const className = constructor.prototype[Symbol.toStringTag];\n    const valueClassName =\n    // We still need to support constructor's name to detect conflicts with older versions of this library.\n    Symbol.toStringTag in value // @ts-expect-error TS bug see, https://github.com/microsoft/TypeScript/issues/38009\n    ? value[Symbol.toStringTag] : (_value$constructor = value.constructor) === null || _value$constructor === void 0 ? void 0 : _value$constructor.name;\n    if (className === valueClassName) {\n      const stringifiedValue = inspect(value);\n      throw new Error(`Cannot use ${className} \"${stringifiedValue}\" from another module or realm.\n\nEnsure that there is only one instance of \"graphql\" in the node_modules\ndirectory. If different versions of \"graphql\" are the dependencies of other\nrelied on modules, use \"resolutions\" to ensure only one version is installed.\n\nhttps://yarnpkg.com/en/docs/selective-version-resolutions\n\nDuplicate \"graphql\" modules cannot be used at the same time since different\nversions may have different capabilities and behavior. The data from one\nversion used in the function from another could produce confusing and\nspurious results.`);\n    }\n  }\n  return false;\n};", "map": {"version": 3, "names": ["inspect", "instanceOf", "globalThis", "process", "env", "NODE_ENV", "value", "constructor", "_value$constructor", "className", "prototype", "Symbol", "toStringTag", "valueClassName", "name", "stringifiedValue", "Error"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/jsutils/instanceOf.mjs"], "sourcesContent": ["import { inspect } from './inspect.mjs';\n/**\n * A replacement for instanceof which includes an error warning when multi-realm\n * constructors are detected.\n * See: https://expressjs.com/en/advanced/best-practice-performance.html#set-node_env-to-production\n * See: https://webpack.js.org/guides/production/\n */\n\nexport const instanceOf =\n  /* c8 ignore next 6 */\n  // FIXME: https://github.com/graphql/graphql-js/issues/2317\n  globalThis.process && globalThis.process.env.NODE_ENV === 'production'\n    ? function instanceOf(value, constructor) {\n        return value instanceof constructor;\n      }\n    : function instanceOf(value, constructor) {\n        if (value instanceof constructor) {\n          return true;\n        }\n\n        if (typeof value === 'object' && value !== null) {\n          var _value$constructor;\n\n          // Prefer Symbol.toStringTag since it is immune to minification.\n          const className = constructor.prototype[Symbol.toStringTag];\n          const valueClassName = // We still need to support constructor's name to detect conflicts with older versions of this library.\n            Symbol.toStringTag in value // @ts-expect-error TS bug see, https://github.com/microsoft/TypeScript/issues/38009\n              ? value[Symbol.toStringTag]\n              : (_value$constructor = value.constructor) === null ||\n                _value$constructor === void 0\n              ? void 0\n              : _value$constructor.name;\n\n          if (className === valueClassName) {\n            const stringifiedValue = inspect(value);\n            throw new Error(`Cannot use ${className} \"${stringifiedValue}\" from another module or realm.\n\nEnsure that there is only one instance of \"graphql\" in the node_modules\ndirectory. If different versions of \"graphql\" are the dependencies of other\nrelied on modules, use \"resolutions\" to ensure only one version is installed.\n\nhttps://yarnpkg.com/en/docs/selective-version-resolutions\n\nDuplicate \"graphql\" modules cannot be used at the same time since different\nversions may have different capabilities and behavior. The data from one\nversion used in the function from another could produce confusing and\nspurious results.`);\n          }\n        }\n\n        return false;\n      };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;AACvC;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMC,UAAU,GACrB;AACA;AACAC,UAAU,CAACC,OAAO,IAAID,UAAU,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAClE,SAASJ,UAAUA,CAACK,KAAK,EAAEC,WAAW,EAAE;EACtC,OAAOD,KAAK,YAAYC,WAAW;AACrC,CAAC,GACD,SAASN,UAAUA,CAACK,KAAK,EAAEC,WAAW,EAAE;EACtC,IAAID,KAAK,YAAYC,WAAW,EAAE;IAChC,OAAO,IAAI;EACb;EAEA,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;IAC/C,IAAIE,kBAAkB;;IAEtB;IACA,MAAMC,SAAS,GAAGF,WAAW,CAACG,SAAS,CAACC,MAAM,CAACC,WAAW,CAAC;IAC3D,MAAMC,cAAc;IAAG;IACrBF,MAAM,CAACC,WAAW,IAAIN,KAAK,CAAC;IAAA,EACxBA,KAAK,CAACK,MAAM,CAACC,WAAW,CAAC,GACzB,CAACJ,kBAAkB,GAAGF,KAAK,CAACC,WAAW,MAAM,IAAI,IACjDC,kBAAkB,KAAK,KAAK,CAAC,GAC7B,KAAK,CAAC,GACNA,kBAAkB,CAACM,IAAI;IAE7B,IAAIL,SAAS,KAAKI,cAAc,EAAE;MAChC,MAAME,gBAAgB,GAAGf,OAAO,CAACM,KAAK,CAAC;MACvC,MAAM,IAAIU,KAAK,CAAE,cAAaP,SAAU,KAAIM,gBAAiB;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,CAAC;IACT;EACF;EAEA,OAAO,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
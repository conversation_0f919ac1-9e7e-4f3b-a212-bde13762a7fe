{"ast": null, "code": "import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextWednesday} function options.\n */\n\n/**\n * @name nextWednesday\n * @category Weekday Helpers\n * @summary When is the next Wednesday?\n *\n * @description\n * When is the next Wednesday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Wednesday\n *\n * @example\n * // When is the next Wednesday after Mar, 22, 2020?\n * const result = nextWednesday(new Date(2020, 2, 22))\n * //=> Wed Mar 25 2020 00:00:00\n */\nexport function nextWednesday(date, options) {\n  return nextDay(date, 3, options);\n}\n\n// Fallback for modularized imports:\nexport default nextWednesday;", "map": {"version": 3, "names": ["nextDay", "nextWednesday", "date", "options"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/date-fns/nextWednesday.js"], "sourcesContent": ["import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextWednesday} function options.\n */\n\n/**\n * @name nextWednesday\n * @category Weekday Helpers\n * @summary When is the next Wednesday?\n *\n * @description\n * When is the next Wednesday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Wednesday\n *\n * @example\n * // When is the next Wednesday after Mar, 22, 2020?\n * const result = nextWednesday(new Date(2020, 2, 22))\n * //=> Wed Mar 25 2020 00:00:00\n */\nexport function nextWednesday(date, options) {\n  return nextDay(date, 3, options);\n}\n\n// Fallback for modularized imports:\nexport default nextWednesday;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;;AAEtC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC3C,OAAOH,OAAO,CAACE,IAAI,EAAE,CAAC,EAAEC,OAAO,CAAC;AAClC;;AAEA;AACA,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
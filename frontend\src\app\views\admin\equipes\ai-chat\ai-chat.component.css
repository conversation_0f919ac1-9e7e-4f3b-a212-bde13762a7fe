.ai-chat-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-body {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  max-height: 500px; /* Augmentation de la hauteur maximale */
}

.message {
  display: flex;
  margin-bottom: 15px;
}

.user-message {
  justify-content: flex-end;
}

.assistant-message {
  justify-content: flex-start;
}

/* Avatar des messages */
.message-avatar {
  width: 36px;
  height: 36px;
  font-size: 1rem;
  color: white;
  flex-shrink: 0;
}

/* Bulle de message */
.message-bubble {
  max-width: 80%;
  word-wrap: break-word;
  position: relative;
  transition: all 0.3s ease;
}

.message-bubble:hover {
  transform: translateY(-2px);
}

.user-bubble {
  background: linear-gradient(45deg, #007bff, #6610f2);
  color: white;
  border-top-right-radius: 0 !important;
}

.assistant-bubble {
  background-color: white;
  color: #343a40;
  border-top-left-radius: 0 !important;
}

/* Horodatage des messages */
.message-time {
  font-size: 0.7rem;
  opacity: 0.7;
}

.user-bubble .message-time {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* Indicateur de chargement */
.typing-indicator {
  display: flex;
  align-items: center;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  background-color: #343a40;
  border-radius: 50%;
  display: inline-block;
  margin-right: 5px;
  animation: typing 1s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
  margin-right: 0;
}

@keyframes typing {
  0% {
    transform: translateY(0px);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-5px);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0px);
    opacity: 0.4;
  }
}

/* Animation de rotation pour l'icône de chargement */
.spin {
  animation: spin 1.5s infinite linear;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.generated-content {
  background-color: #f8f9fa;
  border-radius: 0.25rem;
  max-height: 800px; /* Augmentation de la hauteur maximale */
  overflow-y: auto;
  width: 100%; /* Utilisation de toute la largeur disponible */
}

/* Styles pour l'en-tête des résultats générés */
.generated-header {
  border-left: 5px solid #007bff;
}

/* Styles pour l'affichage amélioré des modules */
.generated-content .module-card {
  transition: all 0.3s ease;
  border: none;
  height: 100%;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
}

.generated-content .module-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

/* Ruban indiquant le numéro du module */
.module-ribbon {
  position: absolute;
  top: 15px;
  right: -35px;
  transform: rotate(45deg);
  width: 150px;
  text-align: center;
  padding: 5px;
  font-size: 0.8rem;
  font-weight: bold;
  color: white;
  z-index: 10;
  box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.generated-content .card-header {
  border-radius: 0;
  font-weight: 600;
  padding: 30px 15px;
  text-align: center;
}

/* Grande icône du module */
.module-icon-large {
  width: 70px;
  height: 70px;
  font-size: 2rem;
  margin: 0 auto;
  position: relative;
  z-index: 5;
}

/* Avatar du membre */
.member-avatar {
  width: 45px;
  height: 45px;
  font-size: 1.2rem;
  flex-shrink: 0;
}

/* Badge d'assignation */
.assignation-badge {
  box-shadow: 0 3px 10px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.assignation-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

/* Boîte de description */
.description-box {
  border-left: 4px solid #e9ecef;
  font-style: italic;
}

/* Styles pour les tâches */
.task-list {
  max-height: 300px;
  overflow-y: auto;
  padding-right: 5px;
  margin-bottom: 10px;
}

.task-header {
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 5;
}

.task-item {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
  background-color: white;
}

.task-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
}

.high-priority {
  border-left-color: #dc3545;
}

.medium-priority {
  border-left-color: #ffc107;
}

.low-priority {
  border-left-color: #17a2b8;
}

.task-title {
  font-weight: 600;
  color: #343a40;
}

.task-description {
  padding-top: 8px;
  border-top: 1px dashed #dee2e6;
  margin-top: 5px;
}

/* Animation d'apparition */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Styles pour la carte de création de tâches */
.create-tasks-card {
  background: linear-gradient(120deg, rgba(255,255,255,1), rgba(248,249,250,1));
  border-left: 5px solid #28a745;
}

/* Cercles d'étapes */
.step-circle {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

/* Bouton de création */
.create-button {
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3) !important;
}

.create-button:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(40, 167, 69, 0.4) !important;
}

.accordion-button:not(.collapsed) {
  background-color: #e7f1ff;
  color: #0d6efd;
}

.accordion-button:focus {
  box-shadow: none;
  border-color: rgba(0,0,0,.125);
}

.chat-input {
  background-color: #fff;
  border-top: 1px solid #dee2e6;
}
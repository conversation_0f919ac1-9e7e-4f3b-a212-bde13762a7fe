{"ast": null, "code": "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/promise.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\nexport default function promiseIterator(promise) {\n  var resolved = false;\n  var iterator = {\n    next: function () {\n      if (resolved) return Promise.resolve({\n        value: undefined,\n        done: true\n      });\n      resolved = true;\n      return new Promise(function (resolve, reject) {\n        promise.then(function (value) {\n          resolve({\n            value: value,\n            done: false\n          });\n        }).catch(reject);\n      });\n    }\n  };\n  if (canUseAsyncIteratorSymbol) {\n    iterator[Symbol.asyncIterator] = function () {\n      return this;\n    };\n  }\n  return iterator;\n}", "map": {"version": 3, "names": ["canUseAsyncIteratorSymbol", "promiseIterator", "promise", "resolved", "iterator", "next", "Promise", "resolve", "value", "undefined", "done", "reject", "then", "catch", "Symbol", "asyncIterator"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/http/iterators/promise.js"], "sourcesContent": ["/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/promise.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\nexport default function promiseIterator(promise) {\n    var resolved = false;\n    var iterator = {\n        next: function () {\n            if (resolved)\n                return Promise.resolve({\n                    value: undefined,\n                    done: true,\n                });\n            resolved = true;\n            return new Promise(function (resolve, reject) {\n                promise\n                    .then(function (value) {\n                    resolve({ value: value, done: false });\n                })\n                    .catch(reject);\n            });\n        },\n    };\n    if (canUseAsyncIteratorSymbol) {\n        iterator[Symbol.asyncIterator] = function () {\n            return this;\n        };\n    }\n    return iterator;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,yBAAyB,QAAQ,6BAA6B;AACvE,eAAe,SAASC,eAAeA,CAACC,OAAO,EAAE;EAC7C,IAAIC,QAAQ,GAAG,KAAK;EACpB,IAAIC,QAAQ,GAAG;IACXC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIF,QAAQ,EACR,OAAOG,OAAO,CAACC,OAAO,CAAC;QACnBC,KAAK,EAAEC,SAAS;QAChBC,IAAI,EAAE;MACV,CAAC,CAAC;MACNP,QAAQ,GAAG,IAAI;MACf,OAAO,IAAIG,OAAO,CAAC,UAAUC,OAAO,EAAEI,MAAM,EAAE;QAC1CT,OAAO,CACFU,IAAI,CAAC,UAAUJ,KAAK,EAAE;UACvBD,OAAO,CAAC;YAAEC,KAAK,EAAEA,KAAK;YAAEE,IAAI,EAAE;UAAM,CAAC,CAAC;QAC1C,CAAC,CAAC,CACGG,KAAK,CAACF,MAAM,CAAC;MACtB,CAAC,CAAC;IACN;EACJ,CAAC;EACD,IAAIX,yBAAyB,EAAE;IAC3BI,QAAQ,CAACU,MAAM,CAACC,aAAa,CAAC,GAAG,YAAY;MACzC,OAAO,IAAI;IACf,CAAC;EACL;EACA,OAAOX,QAAQ;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
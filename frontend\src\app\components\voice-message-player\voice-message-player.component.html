<div
  class="flex items-center p-2 rounded-lg relative overflow-hidden"
  [ngClass]="{
    'bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#6d78c9]/10 dark:to-[#4f5fad]/10':
      !isCurrentUser,
    'bg-gradient-to-r from-[#3d4a85]/20 to-[#4f5fad]/20 dark:from-[#6d78c9]/20 dark:to-[#4f5fad]/20':
      isCurrentUser
  }"
>
  <!-- Élément audio caché -->
  <audio
    #audioPlayer
    [src]="audioUrl"
    (ended)="onAudioEnded()"
    preload="metadata"
    style="display: none"
  ></audio>

  <!-- Decorative background elements -->
  <div class="absolute inset-0 pointer-events-none overflow-hidden">
    <div
      *ngIf="isPlaying"
      class="absolute inset-0 bg-gradient-to-r opacity-30 animate-pulse"
      [ngClass]="
        isCurrentUser
          ? 'from-[#3d4a85]/5 to-[#4f5fad]/5 dark:from-[#6d78c9]/5 dark:to-[#4f5fad]/5'
          : 'from-[#3d4a85]/5 to-[#4f5fad]/5 dark:from-[#6d78c9]/5 dark:to-[#4f5fad]/5'
      "
    ></div>

    <!-- Animated pulse ring when playing -->
    <div
      *ngIf="isPlaying"
      class="absolute -left-4 top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-full animate-ping opacity-30"
      [ngClass]="
        isCurrentUser
          ? 'bg-[#4f5fad] dark:bg-[#6d78c9]'
          : 'bg-[#4f5fad] dark:bg-[#6d78c9]'
      "
    ></div>
  </div>

  <!-- Bouton de lecture/pause avec effet de pulsation -->
  <button
    class="w-10 h-10 rounded-full flex items-center justify-center relative group mr-3 z-10"
    (click)="togglePlay()"
    [ngClass]="{
      'bg-[#4f5fad] dark:bg-[#6d78c9] text-white': isPlaying,
      'bg-[#edf1f4] dark:bg-[#2a2a2a] text-[#4f5fad] dark:text-[#6d78c9]':
        !isPlaying
    }"
  >
    <!-- Glow effect -->
    <div
      class="absolute inset-0 rounded-full blur-md transition-opacity"
      [ngClass]="{
        'opacity-100 bg-[#4f5fad]/50 dark:bg-[#6d78c9]/50': isPlaying,
        'opacity-0 group-hover:opacity-50 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30':
          !isPlaying
      }"
    ></div>

    <i
      class="fas relative z-10 group-hover:scale-110 transition-transform"
      [ngClass]="isPlaying ? 'fa-pause' : 'fa-play'"
    ></i>
  </button>

  <div class="flex-1 relative z-10">
    <!-- Visualisation de l'onde sonore -->
    <div class="relative">
      <!-- Génération de barres pour l'onde sonore -->
      <div class="flex items-center h-8 space-x-0.5">
        <div
          *ngFor="
            let i of [
              0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
              19, 20, 21, 22, 23, 24, 25, 26
            ]
          "
          class="w-1 rounded-full transition-all duration-300"
          [ngClass]="{
            'bg-[#4f5fad] dark:bg-[#6d78c9]':
              isPlaying && progressPercentage > i * 3.7,
            'bg-[#bdc6cc] dark:bg-[#4a4a4a]':
              !isPlaying || progressPercentage <= i * 3.7
          }"
          [style.height.px]="getRandomBarHeight(i)"
          [style.transition-delay.ms]="i * 20"
        >
          <!-- Glow effect for active bars -->
          <div
            *ngIf="isPlaying && progressPercentage > i * 3.7"
            class="absolute inset-0 opacity-70 blur-sm rounded-full -z-10 bg-[#4f5fad]/50 dark:bg-[#6d78c9]/50"
          ></div>
        </div>
      </div>

      <!-- Progress indicator -->
      <div
        class="absolute bottom-0 left-0 h-0.5 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] transition-all duration-300"
        [style.width.%]="progressPercentage"
      ></div>
    </div>

    <!-- Affichage du temps avec animation de fondu -->
    <div
      class="mt-1 text-xs text-right transition-opacity duration-300"
      [ngClass]="{ 'opacity-100': isPlaying, 'opacity-70': !isPlaying }"
    >
      <span class="text-[#4f5fad] dark:text-[#6d78c9] font-medium">
        {{ formattedDuration }}
      </span>
    </div>
  </div>
</div>

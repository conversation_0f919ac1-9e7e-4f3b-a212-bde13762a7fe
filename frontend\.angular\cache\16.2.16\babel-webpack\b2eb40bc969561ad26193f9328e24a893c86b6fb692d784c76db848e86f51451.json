{"ast": null, "code": "import { constructFrom } from \"../../../constructFrom.js\";\nimport { getTimezoneOffsetInMilliseconds } from \"../../../_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { timezonePatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\nimport { parseTimezonePattern } from \"../utils.js\";\n\n// Timezone (ISO-8601. +00:00 is `'Z'`)\nexport class ISOTimezoneWithZParser extends Parser {\n  priority = 10;\n  parse(dateString, token) {\n    switch (token) {\n      case \"X\":\n        return parseTimezonePattern(timezonePatterns.basicOptionalMinutes, dateString);\n      case \"XX\":\n        return parseTimezonePattern(timezonePatterns.basic, dateString);\n      case \"XXXX\":\n        return parseTimezonePattern(timezonePatterns.basicOptionalSeconds, dateString);\n      case \"XXXXX\":\n        return parseTimezonePattern(timezonePatterns.extendedOptionalSeconds, dateString);\n      case \"XXX\":\n      default:\n        return parseTimezonePattern(timezonePatterns.extended, dateString);\n    }\n  }\n  set(date, flags, value) {\n    if (flags.timestampIsSet) return date;\n    return constructFrom(date, date.getTime() - getTimezoneOffsetInMilliseconds(date) - value);\n  }\n  incompatibleTokens = [\"t\", \"T\", \"x\"];\n}", "map": {"version": 3, "names": ["constructFrom", "getTimezoneOffsetInMilliseconds", "timezonePatterns", "<PERSON><PERSON><PERSON>", "parseTimezonePattern", "ISOTimezoneWithZParser", "priority", "parse", "dateString", "token", "basicOptionalMinutes", "basic", "basicOptionalSeconds", "extendedOptionalSeconds", "extended", "set", "date", "flags", "value", "timestampIsSet", "getTime", "incompatibleTokens"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/date-fns/parse/_lib/parsers/ISOTimezoneWithZParser.js"], "sourcesContent": ["import { constructFrom } from \"../../../constructFrom.js\";\nimport { getTimezoneOffsetInMilliseconds } from \"../../../_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { timezonePatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseTimezonePattern } from \"../utils.js\";\n\n// Timezone (ISO-8601. +00:00 is `'Z'`)\nexport class ISOTimezoneWithZParser extends Parser {\n  priority = 10;\n\n  parse(dateString, token) {\n    switch (token) {\n      case \"X\":\n        return parseTimezonePattern(\n          timezonePatterns.basicOptionalMinutes,\n          dateString,\n        );\n      case \"XX\":\n        return parseTimezonePattern(timezonePatterns.basic, dateString);\n      case \"XXXX\":\n        return parseTimezonePattern(\n          timezonePatterns.basicOptionalSeconds,\n          dateString,\n        );\n      case \"XXXXX\":\n        return parseTimezonePattern(\n          timezonePatterns.extendedOptionalSeconds,\n          dateString,\n        );\n      case \"XXX\":\n      default:\n        return parseTimezonePattern(timezonePatterns.extended, dateString);\n    }\n  }\n\n  set(date, flags, value) {\n    if (flags.timestampIsSet) return date;\n    return constructFrom(\n      date,\n      date.getTime() - getTimezoneOffsetInMilliseconds(date) - value,\n    );\n  }\n\n  incompatibleTokens = [\"t\", \"T\", \"x\"];\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,+BAA+B,QAAQ,kDAAkD;AAClG,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,SAASC,MAAM,QAAQ,cAAc;AAErC,SAASC,oBAAoB,QAAQ,aAAa;;AAElD;AACA,OAAO,MAAMC,sBAAsB,SAASF,MAAM,CAAC;EACjDG,QAAQ,GAAG,EAAE;EAEbC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAE;IACvB,QAAQA,KAAK;MACX,KAAK,GAAG;QACN,OAAOL,oBAAoB,CACzBF,gBAAgB,CAACQ,oBAAoB,EACrCF,UACF,CAAC;MACH,KAAK,IAAI;QACP,OAAOJ,oBAAoB,CAACF,gBAAgB,CAACS,KAAK,EAAEH,UAAU,CAAC;MACjE,KAAK,MAAM;QACT,OAAOJ,oBAAoB,CACzBF,gBAAgB,CAACU,oBAAoB,EACrCJ,UACF,CAAC;MACH,KAAK,OAAO;QACV,OAAOJ,oBAAoB,CACzBF,gBAAgB,CAACW,uBAAuB,EACxCL,UACF,CAAC;MACH,KAAK,KAAK;MACV;QACE,OAAOJ,oBAAoB,CAACF,gBAAgB,CAACY,QAAQ,EAAEN,UAAU,CAAC;IACtE;EACF;EAEAO,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAE;IACtB,IAAID,KAAK,CAACE,cAAc,EAAE,OAAOH,IAAI;IACrC,OAAOhB,aAAa,CAClBgB,IAAI,EACJA,IAAI,CAACI,OAAO,CAAC,CAAC,GAAGnB,+BAA+B,CAACe,IAAI,CAAC,GAAGE,KAC3D,CAAC;EACH;EAEAG,kBAAkB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
<!-- Notification message -->
<div
  *ngIf="messageFromRedirect"
  class="fixed top-4 right-4 left-4 md:left-auto md:w-96 bg-white dark:bg-[#1e1e1e] border-l-4 border-[#4f5fad] dark:border-[#6d78c9] rounded-lg p-4 shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.3)] z-50 backdrop-blur-sm"
>
  <div class="flex items-center">
    <div class="relative">
      <i
        class="fas fa-info-circle text-[#4f5fad] dark:text-[#6d78c9] text-lg mr-3"
      ></i>
      <!-- Glow effect -->
      <div
        class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10"
      ></div>
    </div>
    <p class="text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]">
      {{ messageFromRedirect }}
    </p>
  </div>
</div>

<div
  class="min-h-screen main-grid-container flex items-center justify-center p-4 relative overflow-hidden"
  [class.dark]="isDarkMode$ | async"
>
  <!-- Background Grid -->
  <div class="background-grid"></div>

  <!-- Background decorative elements -->
  <div
    class="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none"
  >
    <!-- Decorative circles -->
    <div
      class="absolute top-[10%] left-[5%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/10 to-transparent dark:from-[#6d78c9]/5 dark:to-transparent blur-3xl"
    ></div>
    <div
      class="absolute bottom-[10%] right-[5%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/10 to-transparent dark:from-[#6d78c9]/5 dark:to-transparent blur-3xl"
    ></div>
  </div>

  <div
    class="w-full max-w-4xl bg-white dark:bg-[#1e1e1e] rounded-2xl shadow-xl dark:shadow-[0_10px_30px_rgba(0,0,0,0.3)] overflow-hidden backdrop-blur-sm relative z-10 border border-[#edf1f4]/50 dark:border-[#2a2a2a]"
  >
    <div class="flex flex-col md:flex-row">
      <!-- Image Section -->
      <div
        class="md:w-1/2 bg-gradient-to-br from-[#3d4a85] to-[#4f5fad] dark:from-[#2a3052] dark:to-[#4f5fad] hidden md:flex items-center justify-center p-12 relative overflow-hidden"
      >
        <!-- Decorative elements -->
        <div
          class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full blur-3xl transform translate-x-16 -translate-y-16"
        ></div>
        <div
          class="absolute bottom-0 left-0 w-40 h-40 bg-white/10 rounded-full blur-3xl transform -translate-x-20 translate-y-20"
        ></div>

        <!-- Grid pattern - Harmonisé avec la grille commune -->
        <div class="absolute inset-0 opacity-10">
          <div class="grid grid-cols-12 h-full">
            <div class="border-r border-white/20"></div>
            <div class="border-r border-white/20"></div>
            <div class="border-r border-white/20"></div>
            <div class="border-r border-white/20"></div>
            <div class="border-r border-white/20"></div>
            <div class="border-r border-white/20"></div>
            <div class="border-r border-white/20"></div>
            <div class="border-r border-white/20"></div>
            <div class="border-r border-white/20"></div>
            <div class="border-r border-white/20"></div>
            <div class="border-r border-white/20"></div>
          </div>
          <div class="grid grid-rows-12 w-full absolute top-0 left-0 h-full">
            <div class="border-b border-white/20"></div>
            <div class="border-b border-white/20"></div>
            <div class="border-b border-white/20"></div>
            <div class="border-b border-white/20"></div>
            <div class="border-b border-white/20"></div>
            <div class="border-b border-white/20"></div>
            <div class="border-b border-white/20"></div>
            <div class="border-b border-white/20"></div>
            <div class="border-b border-white/20"></div>
            <div class="border-b border-white/20"></div>
            <div class="border-b border-white/20"></div>
          </div>
        </div>

        <div class="text-center text-white relative z-10">
          <h2 class="text-3xl font-bold mb-4 text-white">
            Espace Administrateur
          </h2>
          <p class="text-white/80">Gestion complète de votre plateforme</p>
          <div class="mt-8 relative">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-24 w-24 mx-auto text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
              />
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
            <!-- Glow effect -->
            <div
              class="absolute inset-0 bg-white/20 blur-xl rounded-full transform scale-150 -z-10"
            ></div>
          </div>
        </div>
      </div>

      <!-- Form Section -->
      <div class="md:w-1/2 p-8 md:p-12 relative">
        <!-- Decorative elements -->
        <div
          class="absolute top-0 right-0 w-20 h-20 bg-[#4f5fad]/5 dark:bg-[#6d78c9]/5 rounded-full blur-2xl"
        ></div>
        <div
          class="absolute bottom-0 left-0 w-24 h-24 bg-[#4f5fad]/5 dark:bg-[#6d78c9]/5 rounded-full blur-2xl"
        ></div>

        <div class="text-center mb-8 relative">
          <h1
            class="text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent"
          >
            Connexion Admin
          </h1>
          <p class="text-[#6d6870] dark:text-[#a0a0a0] mt-2">
            Accédez à votre tableau de bord
          </p>
        </div>

        <form
          #f="ngForm"
          (ngSubmit)="loginAdmin(f)"
          class="space-y-6 relative z-10"
        >
          <!-- Email Input -->
          <div class="group">
            <label
              for="email"
              class="block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1 transition-colors"
              >Email</label
            >
            <div class="relative">
              <input
                id="email"
                type="email"
                name="email"
                #email="ngModel"
                ngModel
                required
                email
                class="w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all"
                placeholder="<EMAIL>"
              />
              <div
                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity"
              >
                <div
                  class="w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full"
                ></div>
              </div>
            </div>
            <div
              *ngIf="email.invalid && (email.dirty || email.touched)"
              class="text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1 flex items-center"
            >
              <i class="fas fa-exclamation-circle mr-1"></i>
              {{ email.errors?.['required'] ? 'Email requis' : 'Format email invalide' }}
            </div>
          </div>

          <!-- Password Input -->
          <div class="group">
            <label
              for="password"
              class="block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1 transition-colors"
              >Mot de passe</label
            >
            <div class="relative">
              <input
                id="password"
                type="password"
                name="password"
                #password="ngModel"
                ngModel
                required
                class="w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all"
                placeholder="••••••••"
              />
              <div
                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity"
              >
                <div
                  class="w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full"
                ></div>
              </div>
            </div>
            <div
              *ngIf="password.invalid && (password.dirty || password.touched)"
              class="text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1 flex items-center"
            >
              <i class="fas fa-exclamation-circle mr-1"></i>
              Mot de passe requis
            </div>
          </div>

          <!-- Error Message -->
          <div
            *ngIf="messageAuthError"
            class="bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 text-[#ff6b69] dark:text-[#ff8785] p-3 rounded-lg text-sm flex items-start"
          >
            <i class="fas fa-exclamation-triangle mt-0.5 mr-2"></i>
            <span>{{ messageAuthError }}</span>
          </div>

          <!-- Submit Button -->
          <button type="submit" class="w-full relative overflow-hidden group">
            <div
              class="absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover:scale-105"
            ></div>
            <div
              class="absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300"
            ></div>
            <span
              class="relative block text-white font-bold py-3 px-4 rounded-lg transition-all"
            >
              Se connecter
            </span>
          </button>
        </form>
      </div>
    </div>
  </div>
</div>

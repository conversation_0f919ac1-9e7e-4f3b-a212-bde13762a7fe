{"ast": null, "code": "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/nodeStream.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\nexport default function nodeStreamIterator(stream) {\n  var cleanup = null;\n  var error = null;\n  var done = false;\n  var data = [];\n  var waiting = [];\n  function onData(chunk) {\n    if (error) return;\n    if (waiting.length) {\n      var shiftedArr = waiting.shift();\n      if (Array.isArray(shiftedArr) && shiftedArr[0]) {\n        return shiftedArr[0]({\n          value: chunk,\n          done: false\n        });\n      }\n    }\n    data.push(chunk);\n  }\n  function onError(err) {\n    error = err;\n    var all = waiting.slice();\n    all.forEach(function (pair) {\n      pair[1](err);\n    });\n    !cleanup || cleanup();\n  }\n  function onEnd() {\n    done = true;\n    var all = waiting.slice();\n    all.forEach(function (pair) {\n      pair[0]({\n        value: undefined,\n        done: true\n      });\n    });\n    !cleanup || cleanup();\n  }\n  cleanup = function () {\n    cleanup = null;\n    stream.removeListener(\"data\", onData);\n    stream.removeListener(\"error\", onError);\n    stream.removeListener(\"end\", onEnd);\n    stream.removeListener(\"finish\", onEnd);\n    stream.removeListener(\"close\", onEnd);\n  };\n  stream.on(\"data\", onData);\n  stream.on(\"error\", onError);\n  stream.on(\"end\", onEnd);\n  stream.on(\"finish\", onEnd);\n  stream.on(\"close\", onEnd);\n  function getNext() {\n    return new Promise(function (resolve, reject) {\n      if (error) return reject(error);\n      if (data.length) return resolve({\n        value: data.shift(),\n        done: false\n      });\n      if (done) return resolve({\n        value: undefined,\n        done: true\n      });\n      waiting.push([resolve, reject]);\n    });\n  }\n  var iterator = {\n    next: function () {\n      return getNext();\n    }\n  };\n  if (canUseAsyncIteratorSymbol) {\n    iterator[Symbol.asyncIterator] = function () {\n      return this;\n    };\n  }\n  return iterator;\n}", "map": {"version": 3, "names": ["canUseAsyncIteratorSymbol", "nodeStreamIterator", "stream", "cleanup", "error", "done", "data", "waiting", "onData", "chunk", "length", "shiftedArr", "shift", "Array", "isArray", "value", "push", "onError", "err", "all", "slice", "for<PERSON>ach", "pair", "onEnd", "undefined", "removeListener", "on", "getNext", "Promise", "resolve", "reject", "iterator", "next", "Symbol", "asyncIterator"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/http/iterators/nodeStream.js"], "sourcesContent": ["/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/nodeStream.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\nexport default function nodeStreamIterator(stream) {\n    var cleanup = null;\n    var error = null;\n    var done = false;\n    var data = [];\n    var waiting = [];\n    function onData(chunk) {\n        if (error)\n            return;\n        if (waiting.length) {\n            var shiftedArr = waiting.shift();\n            if (Array.isArray(shiftedArr) && shiftedArr[0]) {\n                return shiftedArr[0]({ value: chunk, done: false });\n            }\n        }\n        data.push(chunk);\n    }\n    function onError(err) {\n        error = err;\n        var all = waiting.slice();\n        all.forEach(function (pair) {\n            pair[1](err);\n        });\n        !cleanup || cleanup();\n    }\n    function onEnd() {\n        done = true;\n        var all = waiting.slice();\n        all.forEach(function (pair) {\n            pair[0]({ value: undefined, done: true });\n        });\n        !cleanup || cleanup();\n    }\n    cleanup = function () {\n        cleanup = null;\n        stream.removeListener(\"data\", onData);\n        stream.removeListener(\"error\", onError);\n        stream.removeListener(\"end\", onEnd);\n        stream.removeListener(\"finish\", onEnd);\n        stream.removeListener(\"close\", onEnd);\n    };\n    stream.on(\"data\", onData);\n    stream.on(\"error\", onError);\n    stream.on(\"end\", onEnd);\n    stream.on(\"finish\", onEnd);\n    stream.on(\"close\", onEnd);\n    function getNext() {\n        return new Promise(function (resolve, reject) {\n            if (error)\n                return reject(error);\n            if (data.length)\n                return resolve({ value: data.shift(), done: false });\n            if (done)\n                return resolve({ value: undefined, done: true });\n            waiting.push([resolve, reject]);\n        });\n    }\n    var iterator = {\n        next: function () {\n            return getNext();\n        },\n    };\n    if (canUseAsyncIteratorSymbol) {\n        iterator[Symbol.asyncIterator] = function () {\n            return this;\n        };\n    }\n    return iterator;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,yBAAyB,QAAQ,6BAA6B;AACvE,eAAe,SAASC,kBAAkBA,CAACC,MAAM,EAAE;EAC/C,IAAIC,OAAO,GAAG,IAAI;EAClB,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAIC,IAAI,GAAG,KAAK;EAChB,IAAIC,IAAI,GAAG,EAAE;EACb,IAAIC,OAAO,GAAG,EAAE;EAChB,SAASC,MAAMA,CAACC,KAAK,EAAE;IACnB,IAAIL,KAAK,EACL;IACJ,IAAIG,OAAO,CAACG,MAAM,EAAE;MAChB,IAAIC,UAAU,GAAGJ,OAAO,CAACK,KAAK,CAAC,CAAC;MAChC,IAAIC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,EAAE;QAC5C,OAAOA,UAAU,CAAC,CAAC,CAAC,CAAC;UAAEI,KAAK,EAAEN,KAAK;UAAEJ,IAAI,EAAE;QAAM,CAAC,CAAC;MACvD;IACJ;IACAC,IAAI,CAACU,IAAI,CAACP,KAAK,CAAC;EACpB;EACA,SAASQ,OAAOA,CAACC,GAAG,EAAE;IAClBd,KAAK,GAAGc,GAAG;IACX,IAAIC,GAAG,GAAGZ,OAAO,CAACa,KAAK,CAAC,CAAC;IACzBD,GAAG,CAACE,OAAO,CAAC,UAAUC,IAAI,EAAE;MACxBA,IAAI,CAAC,CAAC,CAAC,CAACJ,GAAG,CAAC;IAChB,CAAC,CAAC;IACF,CAACf,OAAO,IAAIA,OAAO,CAAC,CAAC;EACzB;EACA,SAASoB,KAAKA,CAAA,EAAG;IACblB,IAAI,GAAG,IAAI;IACX,IAAIc,GAAG,GAAGZ,OAAO,CAACa,KAAK,CAAC,CAAC;IACzBD,GAAG,CAACE,OAAO,CAAC,UAAUC,IAAI,EAAE;MACxBA,IAAI,CAAC,CAAC,CAAC,CAAC;QAAEP,KAAK,EAAES,SAAS;QAAEnB,IAAI,EAAE;MAAK,CAAC,CAAC;IAC7C,CAAC,CAAC;IACF,CAACF,OAAO,IAAIA,OAAO,CAAC,CAAC;EACzB;EACAA,OAAO,GAAG,SAAAA,CAAA,EAAY;IAClBA,OAAO,GAAG,IAAI;IACdD,MAAM,CAACuB,cAAc,CAAC,MAAM,EAAEjB,MAAM,CAAC;IACrCN,MAAM,CAACuB,cAAc,CAAC,OAAO,EAAER,OAAO,CAAC;IACvCf,MAAM,CAACuB,cAAc,CAAC,KAAK,EAAEF,KAAK,CAAC;IACnCrB,MAAM,CAACuB,cAAc,CAAC,QAAQ,EAAEF,KAAK,CAAC;IACtCrB,MAAM,CAACuB,cAAc,CAAC,OAAO,EAAEF,KAAK,CAAC;EACzC,CAAC;EACDrB,MAAM,CAACwB,EAAE,CAAC,MAAM,EAAElB,MAAM,CAAC;EACzBN,MAAM,CAACwB,EAAE,CAAC,OAAO,EAAET,OAAO,CAAC;EAC3Bf,MAAM,CAACwB,EAAE,CAAC,KAAK,EAAEH,KAAK,CAAC;EACvBrB,MAAM,CAACwB,EAAE,CAAC,QAAQ,EAAEH,KAAK,CAAC;EAC1BrB,MAAM,CAACwB,EAAE,CAAC,OAAO,EAAEH,KAAK,CAAC;EACzB,SAASI,OAAOA,CAAA,EAAG;IACf,OAAO,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;MAC1C,IAAI1B,KAAK,EACL,OAAO0B,MAAM,CAAC1B,KAAK,CAAC;MACxB,IAAIE,IAAI,CAACI,MAAM,EACX,OAAOmB,OAAO,CAAC;QAAEd,KAAK,EAAET,IAAI,CAACM,KAAK,CAAC,CAAC;QAAEP,IAAI,EAAE;MAAM,CAAC,CAAC;MACxD,IAAIA,IAAI,EACJ,OAAOwB,OAAO,CAAC;QAAEd,KAAK,EAAES,SAAS;QAAEnB,IAAI,EAAE;MAAK,CAAC,CAAC;MACpDE,OAAO,CAACS,IAAI,CAAC,CAACa,OAAO,EAAEC,MAAM,CAAC,CAAC;IACnC,CAAC,CAAC;EACN;EACA,IAAIC,QAAQ,GAAG;IACXC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,OAAOL,OAAO,CAAC,CAAC;IACpB;EACJ,CAAC;EACD,IAAI3B,yBAAyB,EAAE;IAC3B+B,QAAQ,CAACE,MAAM,CAACC,aAAa,CAAC,GAAG,YAAY;MACzC,OAAO,IAAI;IACf,CAAC;EACL;EACA,OAAOH,QAAQ;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
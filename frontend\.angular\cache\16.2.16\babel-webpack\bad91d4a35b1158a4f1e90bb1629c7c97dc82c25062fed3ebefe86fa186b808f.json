{"ast": null, "code": "// This currentContext variable will only be used if the makeSlotClass\n// function is called, which happens only if this is the first copy of the\n// @wry/context package to be imported.\nlet currentContext = null;\n// This unique internal object is used to denote the absence of a value\n// for a given Slot, and is never exposed to outside code.\nconst MISSING_VALUE = {};\nlet idCounter = 1;\n// Although we can't do anything about the cost of duplicated code from\n// accidentally bundling multiple copies of the @wry/context package, we can\n// avoid creating the Slot class more than once using makeSlotClass.\nconst makeSlotClass = () => class Slot {\n  constructor() {\n    // If you have a Slot object, you can find out its slot.id, but you cannot\n    // guess the slot.id of a Slot you don't have access to, thanks to the\n    // randomized suffix.\n    this.id = [\"slot\", idCounter++, Date.now(), Math.random().toString(36).slice(2)].join(\":\");\n  }\n  hasValue() {\n    for (let context = currentContext; context; context = context.parent) {\n      // We use the Slot object iself as a key to its value, which means the\n      // value cannot be obtained without a reference to the Slot object.\n      if (this.id in context.slots) {\n        const value = context.slots[this.id];\n        if (value === MISSING_VALUE) break;\n        if (context !== currentContext) {\n          // Cache the value in currentContext.slots so the next lookup will\n          // be faster. This caching is safe because the tree of contexts and\n          // the values of the slots are logically immutable.\n          currentContext.slots[this.id] = value;\n        }\n        return true;\n      }\n    }\n    if (currentContext) {\n      // If a value was not found for this Slot, it's never going to be found\n      // no matter how many times we look it up, so we might as well cache\n      // the absence of the value, too.\n      currentContext.slots[this.id] = MISSING_VALUE;\n    }\n    return false;\n  }\n  getValue() {\n    if (this.hasValue()) {\n      return currentContext.slots[this.id];\n    }\n  }\n  withValue(value, callback,\n  // Given the prevalence of arrow functions, specifying arguments is likely\n  // to be much more common than specifying `this`, hence this ordering:\n  args, thisArg) {\n    const slots = {\n      __proto__: null,\n      [this.id]: value\n    };\n    const parent = currentContext;\n    currentContext = {\n      parent,\n      slots\n    };\n    try {\n      // Function.prototype.apply allows the arguments array argument to be\n      // omitted or undefined, so args! is fine here.\n      return callback.apply(thisArg, args);\n    } finally {\n      currentContext = parent;\n    }\n  }\n  // Capture the current context and wrap a callback function so that it\n  // reestablishes the captured context when called.\n  static bind(callback) {\n    const context = currentContext;\n    return function () {\n      const saved = currentContext;\n      try {\n        currentContext = context;\n        return callback.apply(this, arguments);\n      } finally {\n        currentContext = saved;\n      }\n    };\n  }\n  // Immediately run a callback function without any captured context.\n  static noContext(callback,\n  // Given the prevalence of arrow functions, specifying arguments is likely\n  // to be much more common than specifying `this`, hence this ordering:\n  args, thisArg) {\n    if (currentContext) {\n      const saved = currentContext;\n      try {\n        currentContext = null;\n        // Function.prototype.apply allows the arguments array argument to be\n        // omitted or undefined, so args! is fine here.\n        return callback.apply(thisArg, args);\n      } finally {\n        currentContext = saved;\n      }\n    } else {\n      return callback.apply(thisArg, args);\n    }\n  }\n};\nfunction maybe(fn) {\n  try {\n    return fn();\n  } catch (ignored) {}\n}\n// We store a single global implementation of the Slot class as a permanent\n// non-enumerable property of the globalThis object. This obfuscation does\n// nothing to prevent access to the Slot class, but at least it ensures the\n// implementation (i.e. currentContext) cannot be tampered with, and all copies\n// of the @wry/context package (hopefully just one) will share the same Slot\n// implementation. Since the first copy of the @wry/context package to be\n// imported wins, this technique imposes a steep cost for any future breaking\n// changes to the Slot class.\nconst globalKey = \"@wry/context:Slot\";\nconst host =\n// Prefer globalThis when available.\n// https://github.com/benjamn/wryware/issues/347\nmaybe(() => globalThis) ||\n// Fall back to global, which works in Node.js and may be converted by some\n// bundlers to the appropriate identifier (window, self, ...) depending on the\n// bundling target. https://github.com/endojs/endo/issues/576#issuecomment-1178515224\nmaybe(() => global) ||\n// Otherwise, use a dummy host that's local to this module. We used to fall\n// back to using the Array constructor as a namespace, but that was flagged in\n// https://github.com/benjamn/wryware/issues/347, and can be avoided.\nObject.create(null);\n// Whichever globalHost we're using, make TypeScript happy about the additional\n// globalKey property.\nconst globalHost = host;\nexport const Slot = globalHost[globalKey] ||\n// Earlier versions of this package stored the globalKey property on the Array\n// constructor, so we check there as well, to prevent Slot class duplication.\nArray[globalKey] || function (Slot) {\n  try {\n    Object.defineProperty(globalHost, globalKey, {\n      value: Slot,\n      enumerable: false,\n      writable: false,\n      // When it was possible for globalHost to be the Array constructor (a\n      // legacy Slot dedup strategy), it was important for the property to be\n      // configurable:true so it could be deleted. That does not seem to be as\n      // important when globalHost is the global object, but I don't want to\n      // cause similar problems again, and configurable:true seems safest.\n      // https://github.com/endojs/endo/issues/576#issuecomment-1178274008\n      configurable: true\n    });\n  } finally {\n    return Slot;\n  }\n}(makeSlotClass());", "map": {"version": 3, "names": ["currentContext", "MISSING_VALUE", "idCounter", "makeSlotClass", "Slot", "constructor", "id", "Date", "now", "Math", "random", "toString", "slice", "join", "hasValue", "context", "parent", "slots", "value", "getValue", "with<PERSON><PERSON><PERSON>", "callback", "args", "thisArg", "__proto__", "apply", "bind", "saved", "arguments", "noContext", "maybe", "fn", "ignored", "globalKey", "host", "globalThis", "global", "Object", "create", "globalHost", "Array", "defineProperty", "enumerable", "writable", "configurable"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@wry/context/lib/slot.js"], "sourcesContent": ["// This currentContext variable will only be used if the makeSlotClass\n// function is called, which happens only if this is the first copy of the\n// @wry/context package to be imported.\nlet currentContext = null;\n// This unique internal object is used to denote the absence of a value\n// for a given Slot, and is never exposed to outside code.\nconst MISSING_VALUE = {};\nlet idCounter = 1;\n// Although we can't do anything about the cost of duplicated code from\n// accidentally bundling multiple copies of the @wry/context package, we can\n// avoid creating the Slot class more than once using makeSlotClass.\nconst makeSlotClass = () => class Slot {\n    constructor() {\n        // If you have a Slot object, you can find out its slot.id, but you cannot\n        // guess the slot.id of a Slot you don't have access to, thanks to the\n        // randomized suffix.\n        this.id = [\n            \"slot\",\n            idCounter++,\n            Date.now(),\n            Math.random().toString(36).slice(2),\n        ].join(\":\");\n    }\n    hasValue() {\n        for (let context = currentContext; context; context = context.parent) {\n            // We use the Slot object iself as a key to its value, which means the\n            // value cannot be obtained without a reference to the Slot object.\n            if (this.id in context.slots) {\n                const value = context.slots[this.id];\n                if (value === MISSING_VALUE)\n                    break;\n                if (context !== currentContext) {\n                    // Cache the value in currentContext.slots so the next lookup will\n                    // be faster. This caching is safe because the tree of contexts and\n                    // the values of the slots are logically immutable.\n                    currentContext.slots[this.id] = value;\n                }\n                return true;\n            }\n        }\n        if (currentContext) {\n            // If a value was not found for this Slot, it's never going to be found\n            // no matter how many times we look it up, so we might as well cache\n            // the absence of the value, too.\n            currentContext.slots[this.id] = MISSING_VALUE;\n        }\n        return false;\n    }\n    getValue() {\n        if (this.hasValue()) {\n            return currentContext.slots[this.id];\n        }\n    }\n    withValue(value, callback, \n    // Given the prevalence of arrow functions, specifying arguments is likely\n    // to be much more common than specifying `this`, hence this ordering:\n    args, thisArg) {\n        const slots = {\n            __proto__: null,\n            [this.id]: value,\n        };\n        const parent = currentContext;\n        currentContext = { parent, slots };\n        try {\n            // Function.prototype.apply allows the arguments array argument to be\n            // omitted or undefined, so args! is fine here.\n            return callback.apply(thisArg, args);\n        }\n        finally {\n            currentContext = parent;\n        }\n    }\n    // Capture the current context and wrap a callback function so that it\n    // reestablishes the captured context when called.\n    static bind(callback) {\n        const context = currentContext;\n        return function () {\n            const saved = currentContext;\n            try {\n                currentContext = context;\n                return callback.apply(this, arguments);\n            }\n            finally {\n                currentContext = saved;\n            }\n        };\n    }\n    // Immediately run a callback function without any captured context.\n    static noContext(callback, \n    // Given the prevalence of arrow functions, specifying arguments is likely\n    // to be much more common than specifying `this`, hence this ordering:\n    args, thisArg) {\n        if (currentContext) {\n            const saved = currentContext;\n            try {\n                currentContext = null;\n                // Function.prototype.apply allows the arguments array argument to be\n                // omitted or undefined, so args! is fine here.\n                return callback.apply(thisArg, args);\n            }\n            finally {\n                currentContext = saved;\n            }\n        }\n        else {\n            return callback.apply(thisArg, args);\n        }\n    }\n};\nfunction maybe(fn) {\n    try {\n        return fn();\n    }\n    catch (ignored) { }\n}\n// We store a single global implementation of the Slot class as a permanent\n// non-enumerable property of the globalThis object. This obfuscation does\n// nothing to prevent access to the Slot class, but at least it ensures the\n// implementation (i.e. currentContext) cannot be tampered with, and all copies\n// of the @wry/context package (hopefully just one) will share the same Slot\n// implementation. Since the first copy of the @wry/context package to be\n// imported wins, this technique imposes a steep cost for any future breaking\n// changes to the Slot class.\nconst globalKey = \"@wry/context:Slot\";\nconst host = \n// Prefer globalThis when available.\n// https://github.com/benjamn/wryware/issues/347\nmaybe(() => globalThis) ||\n    // Fall back to global, which works in Node.js and may be converted by some\n    // bundlers to the appropriate identifier (window, self, ...) depending on the\n    // bundling target. https://github.com/endojs/endo/issues/576#issuecomment-1178515224\n    maybe(() => global) ||\n    // Otherwise, use a dummy host that's local to this module. We used to fall\n    // back to using the Array constructor as a namespace, but that was flagged in\n    // https://github.com/benjamn/wryware/issues/347, and can be avoided.\n    Object.create(null);\n// Whichever globalHost we're using, make TypeScript happy about the additional\n// globalKey property.\nconst globalHost = host;\nexport const Slot = globalHost[globalKey] ||\n    // Earlier versions of this package stored the globalKey property on the Array\n    // constructor, so we check there as well, to prevent Slot class duplication.\n    Array[globalKey] ||\n    (function (Slot) {\n        try {\n            Object.defineProperty(globalHost, globalKey, {\n                value: Slot,\n                enumerable: false,\n                writable: false,\n                // When it was possible for globalHost to be the Array constructor (a\n                // legacy Slot dedup strategy), it was important for the property to be\n                // configurable:true so it could be deleted. That does not seem to be as\n                // important when globalHost is the global object, but I don't want to\n                // cause similar problems again, and configurable:true seems safest.\n                // https://github.com/endojs/endo/issues/576#issuecomment-1178274008\n                configurable: true\n            });\n        }\n        finally {\n            return Slot;\n        }\n    })(makeSlotClass());\n"], "mappings": "AAAA;AACA;AACA;AACA,IAAIA,cAAc,GAAG,IAAI;AACzB;AACA;AACA,MAAMC,aAAa,GAAG,CAAC,CAAC;AACxB,IAAIC,SAAS,GAAG,CAAC;AACjB;AACA;AACA;AACA,MAAMC,aAAa,GAAGA,CAAA,KAAM,MAAMC,IAAI,CAAC;EACnCC,WAAWA,CAAA,EAAG;IACV;IACA;IACA;IACA,IAAI,CAACC,EAAE,GAAG,CACN,MAAM,EACNJ,SAAS,EAAE,EACXK,IAAI,CAACC,GAAG,CAAC,CAAC,EACVC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CACtC,CAACC,IAAI,CAAC,GAAG,CAAC;EACf;EACAC,QAAQA,CAAA,EAAG;IACP,KAAK,IAAIC,OAAO,GAAGf,cAAc,EAAEe,OAAO,EAAEA,OAAO,GAAGA,OAAO,CAACC,MAAM,EAAE;MAClE;MACA;MACA,IAAI,IAAI,CAACV,EAAE,IAAIS,OAAO,CAACE,KAAK,EAAE;QAC1B,MAAMC,KAAK,GAAGH,OAAO,CAACE,KAAK,CAAC,IAAI,CAACX,EAAE,CAAC;QACpC,IAAIY,KAAK,KAAKjB,aAAa,EACvB;QACJ,IAAIc,OAAO,KAAKf,cAAc,EAAE;UAC5B;UACA;UACA;UACAA,cAAc,CAACiB,KAAK,CAAC,IAAI,CAACX,EAAE,CAAC,GAAGY,KAAK;QACzC;QACA,OAAO,IAAI;MACf;IACJ;IACA,IAAIlB,cAAc,EAAE;MAChB;MACA;MACA;MACAA,cAAc,CAACiB,KAAK,CAAC,IAAI,CAACX,EAAE,CAAC,GAAGL,aAAa;IACjD;IACA,OAAO,KAAK;EAChB;EACAkB,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACL,QAAQ,CAAC,CAAC,EAAE;MACjB,OAAOd,cAAc,CAACiB,KAAK,CAAC,IAAI,CAACX,EAAE,CAAC;IACxC;EACJ;EACAc,SAASA,CAACF,KAAK,EAAEG,QAAQ;EACzB;EACA;EACAC,IAAI,EAAEC,OAAO,EAAE;IACX,MAAMN,KAAK,GAAG;MACVO,SAAS,EAAE,IAAI;MACf,CAAC,IAAI,CAAClB,EAAE,GAAGY;IACf,CAAC;IACD,MAAMF,MAAM,GAAGhB,cAAc;IAC7BA,cAAc,GAAG;MAAEgB,MAAM;MAAEC;IAAM,CAAC;IAClC,IAAI;MACA;MACA;MACA,OAAOI,QAAQ,CAACI,KAAK,CAACF,OAAO,EAAED,IAAI,CAAC;IACxC,CAAC,SACO;MACJtB,cAAc,GAAGgB,MAAM;IAC3B;EACJ;EACA;EACA;EACA,OAAOU,IAAIA,CAACL,QAAQ,EAAE;IAClB,MAAMN,OAAO,GAAGf,cAAc;IAC9B,OAAO,YAAY;MACf,MAAM2B,KAAK,GAAG3B,cAAc;MAC5B,IAAI;QACAA,cAAc,GAAGe,OAAO;QACxB,OAAOM,QAAQ,CAACI,KAAK,CAAC,IAAI,EAAEG,SAAS,CAAC;MAC1C,CAAC,SACO;QACJ5B,cAAc,GAAG2B,KAAK;MAC1B;IACJ,CAAC;EACL;EACA;EACA,OAAOE,SAASA,CAACR,QAAQ;EACzB;EACA;EACAC,IAAI,EAAEC,OAAO,EAAE;IACX,IAAIvB,cAAc,EAAE;MAChB,MAAM2B,KAAK,GAAG3B,cAAc;MAC5B,IAAI;QACAA,cAAc,GAAG,IAAI;QACrB;QACA;QACA,OAAOqB,QAAQ,CAACI,KAAK,CAACF,OAAO,EAAED,IAAI,CAAC;MACxC,CAAC,SACO;QACJtB,cAAc,GAAG2B,KAAK;MAC1B;IACJ,CAAC,MACI;MACD,OAAON,QAAQ,CAACI,KAAK,CAACF,OAAO,EAAED,IAAI,CAAC;IACxC;EACJ;AACJ,CAAC;AACD,SAASQ,KAAKA,CAACC,EAAE,EAAE;EACf,IAAI;IACA,OAAOA,EAAE,CAAC,CAAC;EACf,CAAC,CACD,OAAOC,OAAO,EAAE,CAAE;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG,mBAAmB;AACrC,MAAMC,IAAI;AACV;AACA;AACAJ,KAAK,CAAC,MAAMK,UAAU,CAAC;AACnB;AACA;AACA;AACAL,KAAK,CAAC,MAAMM,MAAM,CAAC;AACnB;AACA;AACA;AACAC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AACvB;AACA;AACA,MAAMC,UAAU,GAAGL,IAAI;AACvB,OAAO,MAAM9B,IAAI,GAAGmC,UAAU,CAACN,SAAS,CAAC;AACrC;AACA;AACAO,KAAK,CAACP,SAAS,CAAC,IACf,UAAU7B,IAAI,EAAE;EACb,IAAI;IACAiC,MAAM,CAACI,cAAc,CAACF,UAAU,EAAEN,SAAS,EAAE;MACzCf,KAAK,EAAEd,IAAI;MACXsC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,KAAK;MACf;MACA;MACA;MACA;MACA;MACA;MACAC,YAAY,EAAE;IAClB,CAAC,CAAC;EACN,CAAC,SACO;IACJ,OAAOxC,IAAI;EACf;AACJ,CAAC,CAAED,aAAa,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
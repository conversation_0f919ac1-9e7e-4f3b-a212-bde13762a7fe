{"ast": null, "code": "import { Slot } from \"./slot.js\";\nexport { Slot };\nexport const {\n  bind,\n  noContext\n} = Slot;\n// Like global.setTimeout, except the callback runs with captured context.\nexport { setTimeoutWithContext as setTimeout };\nfunction setTimeoutWithContext(callback, delay) {\n  return setTimeout(bind(callback), delay);\n}\n// Turn any generator function into an async function (using yield instead\n// of await), with context automatically preserved across yields.\nexport function asyncFromGen(genFn) {\n  return function () {\n    const gen = genFn.apply(this, arguments);\n    const boundNext = bind(gen.next);\n    const boundThrow = bind(gen.throw);\n    return new Promise((resolve, reject) => {\n      function invoke(method, argument) {\n        try {\n          var result = method.call(gen, argument);\n        } catch (error) {\n          return reject(error);\n        }\n        const next = result.done ? resolve : invokeNext;\n        if (isPromiseLike(result.value)) {\n          result.value.then(next, result.done ? reject : invokeThrow);\n        } else {\n          next(result.value);\n        }\n      }\n      const invokeNext = value => invoke(boundNext, value);\n      const invokeThrow = error => invoke(boundThrow, error);\n      invokeNext();\n    });\n  };\n}\nfunction isPromiseLike(value) {\n  return value && typeof value.then === \"function\";\n}\n// If you use the fibers npm package to implement coroutines in Node.js,\n// you should call this function at least once to ensure context management\n// remains coherent across any yields.\nconst wrappedFibers = [];\nexport function wrapYieldingFiberMethods(Fiber) {\n  // There can be only one implementation of Fiber per process, so this array\n  // should never grow longer than one element.\n  if (wrappedFibers.indexOf(Fiber) < 0) {\n    const wrap = (obj, method) => {\n      const fn = obj[method];\n      obj[method] = function () {\n        return noContext(fn, arguments, this);\n      };\n    };\n    // These methods can yield, according to\n    // https://github.com/laverdet/node-fibers/blob/ddebed9b8ae3883e57f822e2108e6943e5c8d2a8/fibers.js#L97-L100\n    wrap(Fiber, \"yield\");\n    wrap(Fiber.prototype, \"run\");\n    wrap(Fiber.prototype, \"throwInto\");\n    wrappedFibers.push(Fiber);\n  }\n  return Fiber;\n}", "map": {"version": 3, "names": ["Slot", "bind", "noContext", "setTimeoutWithContext", "setTimeout", "callback", "delay", "asyncFromGen", "genFn", "gen", "apply", "arguments", "boundNext", "next", "boundThrow", "throw", "Promise", "resolve", "reject", "invoke", "method", "argument", "result", "call", "error", "done", "invokeNext", "isPromiseLike", "value", "then", "invokeThrow", "<PERSON><PERSON><PERSON><PERSON>", "wrapYieldingFiberMethods", "Fiber", "indexOf", "wrap", "obj", "fn", "prototype", "push"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@wry/context/lib/index.js"], "sourcesContent": ["import { Slot } from \"./slot.js\";\nexport { Slot };\nexport const { bind, noContext } = Slot;\n// Like global.setTimeout, except the callback runs with captured context.\nexport { setTimeoutWithContext as setTimeout };\nfunction setTimeoutWithContext(callback, delay) {\n    return setTimeout(bind(callback), delay);\n}\n// Turn any generator function into an async function (using yield instead\n// of await), with context automatically preserved across yields.\nexport function asyncFromGen(genFn) {\n    return function () {\n        const gen = genFn.apply(this, arguments);\n        const boundNext = bind(gen.next);\n        const boundThrow = bind(gen.throw);\n        return new Promise((resolve, reject) => {\n            function invoke(method, argument) {\n                try {\n                    var result = method.call(gen, argument);\n                }\n                catch (error) {\n                    return reject(error);\n                }\n                const next = result.done ? resolve : invokeNext;\n                if (isPromiseLike(result.value)) {\n                    result.value.then(next, result.done ? reject : invokeThrow);\n                }\n                else {\n                    next(result.value);\n                }\n            }\n            const invokeNext = (value) => invoke(boundNext, value);\n            const invokeThrow = (error) => invoke(boundThrow, error);\n            invokeNext();\n        });\n    };\n}\nfunction isPromiseLike(value) {\n    return value && typeof value.then === \"function\";\n}\n// If you use the fibers npm package to implement coroutines in Node.js,\n// you should call this function at least once to ensure context management\n// remains coherent across any yields.\nconst wrappedFibers = [];\nexport function wrapYieldingFiberMethods(Fiber) {\n    // There can be only one implementation of Fiber per process, so this array\n    // should never grow longer than one element.\n    if (wrappedFibers.indexOf(Fiber) < 0) {\n        const wrap = (obj, method) => {\n            const fn = obj[method];\n            obj[method] = function () {\n                return noContext(fn, arguments, this);\n            };\n        };\n        // These methods can yield, according to\n        // https://github.com/laverdet/node-fibers/blob/ddebed9b8ae3883e57f822e2108e6943e5c8d2a8/fibers.js#L97-L100\n        wrap(Fiber, \"yield\");\n        wrap(Fiber.prototype, \"run\");\n        wrap(Fiber.prototype, \"throwInto\");\n        wrappedFibers.push(Fiber);\n    }\n    return Fiber;\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAChC,SAASA,IAAI;AACb,OAAO,MAAM;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGF,IAAI;AACvC;AACA,SAASG,qBAAqB,IAAIC,UAAU;AAC5C,SAASD,qBAAqBA,CAACE,QAAQ,EAAEC,KAAK,EAAE;EAC5C,OAAOF,UAAU,CAACH,IAAI,CAACI,QAAQ,CAAC,EAAEC,KAAK,CAAC;AAC5C;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,KAAK,EAAE;EAChC,OAAO,YAAY;IACf,MAAMC,GAAG,GAAGD,KAAK,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACxC,MAAMC,SAAS,GAAGX,IAAI,CAACQ,GAAG,CAACI,IAAI,CAAC;IAChC,MAAMC,UAAU,GAAGb,IAAI,CAACQ,GAAG,CAACM,KAAK,CAAC;IAClC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC,SAASC,MAAMA,CAACC,MAAM,EAAEC,QAAQ,EAAE;QAC9B,IAAI;UACA,IAAIC,MAAM,GAAGF,MAAM,CAACG,IAAI,CAACd,GAAG,EAAEY,QAAQ,CAAC;QAC3C,CAAC,CACD,OAAOG,KAAK,EAAE;UACV,OAAON,MAAM,CAACM,KAAK,CAAC;QACxB;QACA,MAAMX,IAAI,GAAGS,MAAM,CAACG,IAAI,GAAGR,OAAO,GAAGS,UAAU;QAC/C,IAAIC,aAAa,CAACL,MAAM,CAACM,KAAK,CAAC,EAAE;UAC7BN,MAAM,CAACM,KAAK,CAACC,IAAI,CAAChB,IAAI,EAAES,MAAM,CAACG,IAAI,GAAGP,MAAM,GAAGY,WAAW,CAAC;QAC/D,CAAC,MACI;UACDjB,IAAI,CAACS,MAAM,CAACM,KAAK,CAAC;QACtB;MACJ;MACA,MAAMF,UAAU,GAAIE,KAAK,IAAKT,MAAM,CAACP,SAAS,EAAEgB,KAAK,CAAC;MACtD,MAAME,WAAW,GAAIN,KAAK,IAAKL,MAAM,CAACL,UAAU,EAAEU,KAAK,CAAC;MACxDE,UAAU,CAAC,CAAC;IAChB,CAAC,CAAC;EACN,CAAC;AACL;AACA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC1B,OAAOA,KAAK,IAAI,OAAOA,KAAK,CAACC,IAAI,KAAK,UAAU;AACpD;AACA;AACA;AACA;AACA,MAAME,aAAa,GAAG,EAAE;AACxB,OAAO,SAASC,wBAAwBA,CAACC,KAAK,EAAE;EAC5C;EACA;EACA,IAAIF,aAAa,CAACG,OAAO,CAACD,KAAK,CAAC,GAAG,CAAC,EAAE;IAClC,MAAME,IAAI,GAAGA,CAACC,GAAG,EAAEhB,MAAM,KAAK;MAC1B,MAAMiB,EAAE,GAAGD,GAAG,CAAChB,MAAM,CAAC;MACtBgB,GAAG,CAAChB,MAAM,CAAC,GAAG,YAAY;QACtB,OAAOlB,SAAS,CAACmC,EAAE,EAAE1B,SAAS,EAAE,IAAI,CAAC;MACzC,CAAC;IACL,CAAC;IACD;IACA;IACAwB,IAAI,CAACF,KAAK,EAAE,OAAO,CAAC;IACpBE,IAAI,CAACF,KAAK,CAACK,SAAS,EAAE,KAAK,CAAC;IAC5BH,IAAI,CAACF,KAAK,CAACK,SAAS,EAAE,WAAW,CAAC;IAClCP,aAAa,CAACQ,IAAI,CAACN,KAAK,CAAC;EAC7B;EACA,OAAOA,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
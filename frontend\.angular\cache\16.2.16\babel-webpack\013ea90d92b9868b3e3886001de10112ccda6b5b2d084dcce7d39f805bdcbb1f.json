{"ast": null, "code": "/**\n * Return true if `value` is object-like. A value is object-like if it's not\n * `null` and has a `typeof` result of \"object\".\n */\nexport function isObjectLike(value) {\n  return typeof value == 'object' && value !== null;\n}", "map": {"version": 3, "names": ["isObjectLike", "value"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/jsutils/isObjectLike.mjs"], "sourcesContent": ["/**\n * Return true if `value` is object-like. A value is object-like if it's not\n * `null` and has a `typeof` result of \"object\".\n */\nexport function isObjectLike(value) {\n  return typeof value == 'object' && value !== null;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,YAAYA,CAACC,KAAK,EAAE;EAClC,OAAO,OAAOA,KAAK,IAAI,QAAQ,IAAIA,KAAK,KAAK,IAAI;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { daysInYear } from \"./constants.js\";\n\n/**\n * @name yearsToDays\n * @category Conversion Helpers\n * @summary Convert years to days.\n *\n * @description\n * Convert a number of years to a full number of days.\n *\n * @param years - The number of years to be converted\n *\n * @returns The number of years converted in days\n *\n * @example\n * // Convert 2 years into days\n * const result = yearsToDays(2)\n * //=> 730\n */\nexport function yearsToDays(years) {\n  return Math.trunc(years * daysInYear);\n}\n\n// Fallback for modularized imports:\nexport default yearsToDays;", "map": {"version": 3, "names": ["daysInYear", "yearsToDays", "years", "Math", "trunc"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/date-fns/yearsToDays.js"], "sourcesContent": ["import { daysInYear } from \"./constants.js\";\n\n/**\n * @name yearsToDays\n * @category Conversion Helpers\n * @summary Convert years to days.\n *\n * @description\n * Convert a number of years to a full number of days.\n *\n * @param years - The number of years to be converted\n *\n * @returns The number of years converted in days\n *\n * @example\n * // Convert 2 years into days\n * const result = yearsToDays(2)\n * //=> 730\n */\nexport function yearsToDays(years) {\n  return Math.trunc(years * daysInYear);\n}\n\n// Fallback for modularized imports:\nexport default yearsToDays;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,gBAAgB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,OAAOC,IAAI,CAACC,KAAK,CAACF,KAAK,GAAGF,UAAU,CAAC;AACvC;;AAEA;AACA,eAAeC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { parentEntrySlot } from \"./context.js\";\nimport { hasOwnProperty, maybeUnsubscribe, arrayFromSet } from \"./helpers.js\";\nconst EntryMethods = {\n  setDirty: true,\n  dispose: true,\n  forget: true // Fully remove parent Entry from LRU cache and computation graph\n};\n\nexport function dep(options) {\n  const depsByKey = new Map();\n  const subscribe = options && options.subscribe;\n  function depend(key) {\n    const parent = parentEntrySlot.getValue();\n    if (parent) {\n      let dep = depsByKey.get(key);\n      if (!dep) {\n        depsByKey.set(key, dep = new Set());\n      }\n      parent.dependOn(dep);\n      if (typeof subscribe === \"function\") {\n        maybeUnsubscribe(dep);\n        dep.unsubscribe = subscribe(key);\n      }\n    }\n  }\n  depend.dirty = function dirty(key, entryMethodName) {\n    const dep = depsByKey.get(key);\n    if (dep) {\n      const m = entryMethodName && hasOwnProperty.call(EntryMethods, entryMethodName) ? entryMethodName : \"setDirty\";\n      // We have to use arrayFromSet(dep).forEach instead of dep.forEach,\n      // because modifying a Set while iterating over it can cause elements in\n      // the Set to be removed from the Set before they've been iterated over.\n      arrayFromSet(dep).forEach(entry => entry[m]());\n      depsByKey.delete(key);\n      maybeUnsubscribe(dep);\n    }\n  };\n  return depend;\n}", "map": {"version": 3, "names": ["parentEntrySlot", "hasOwnProperty", "maybeUnsubscribe", "arrayFromSet", "EntryMethods", "set<PERSON>irty", "dispose", "forget", "dep", "options", "depsByKey", "Map", "subscribe", "depend", "key", "parent", "getValue", "get", "set", "Set", "dependOn", "unsubscribe", "dirty", "entryMethodName", "m", "call", "for<PERSON>ach", "entry", "delete"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/optimism/lib/dep.js"], "sourcesContent": ["import { parentEntrySlot } from \"./context.js\";\nimport { hasOwnProperty, maybeUnsubscribe, arrayFromSet, } from \"./helpers.js\";\nconst EntryMethods = {\n    setDirty: true,\n    dispose: true,\n    forget: true, // Fully remove parent Entry from LRU cache and computation graph\n};\nexport function dep(options) {\n    const depsByKey = new Map();\n    const subscribe = options && options.subscribe;\n    function depend(key) {\n        const parent = parentEntrySlot.getValue();\n        if (parent) {\n            let dep = depsByKey.get(key);\n            if (!dep) {\n                depsByKey.set(key, dep = new Set);\n            }\n            parent.dependOn(dep);\n            if (typeof subscribe === \"function\") {\n                maybeUnsubscribe(dep);\n                dep.unsubscribe = subscribe(key);\n            }\n        }\n    }\n    depend.dirty = function dirty(key, entryMethodName) {\n        const dep = depsByKey.get(key);\n        if (dep) {\n            const m = (entryMethodName &&\n                hasOwnProperty.call(EntryMethods, entryMethodName)) ? entryMethodName : \"setDirty\";\n            // We have to use arrayFromSet(dep).forEach instead of dep.forEach,\n            // because modifying a Set while iterating over it can cause elements in\n            // the Set to be removed from the Set before they've been iterated over.\n            arrayFromSet(dep).forEach(entry => entry[m]());\n            depsByKey.delete(key);\n            maybeUnsubscribe(dep);\n        }\n    };\n    return depend;\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,cAAc;AAC9C,SAASC,cAAc,EAAEC,gBAAgB,EAAEC,YAAY,QAAS,cAAc;AAC9E,MAAMC,YAAY,GAAG;EACjBC,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,IAAI;EACbC,MAAM,EAAE,IAAI,CAAE;AAClB,CAAC;;AACD,OAAO,SAASC,GAAGA,CAACC,OAAO,EAAE;EACzB,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC3B,MAAMC,SAAS,GAAGH,OAAO,IAAIA,OAAO,CAACG,SAAS;EAC9C,SAASC,MAAMA,CAACC,GAAG,EAAE;IACjB,MAAMC,MAAM,GAAGf,eAAe,CAACgB,QAAQ,CAAC,CAAC;IACzC,IAAID,MAAM,EAAE;MACR,IAAIP,GAAG,GAAGE,SAAS,CAACO,GAAG,CAACH,GAAG,CAAC;MAC5B,IAAI,CAACN,GAAG,EAAE;QACNE,SAAS,CAACQ,GAAG,CAACJ,GAAG,EAAEN,GAAG,GAAG,IAAIW,GAAG,CAAD,CAAC,CAAC;MACrC;MACAJ,MAAM,CAACK,QAAQ,CAACZ,GAAG,CAAC;MACpB,IAAI,OAAOI,SAAS,KAAK,UAAU,EAAE;QACjCV,gBAAgB,CAACM,GAAG,CAAC;QACrBA,GAAG,CAACa,WAAW,GAAGT,SAAS,CAACE,GAAG,CAAC;MACpC;IACJ;EACJ;EACAD,MAAM,CAACS,KAAK,GAAG,SAASA,KAAKA,CAACR,GAAG,EAAES,eAAe,EAAE;IAChD,MAAMf,GAAG,GAAGE,SAAS,CAACO,GAAG,CAACH,GAAG,CAAC;IAC9B,IAAIN,GAAG,EAAE;MACL,MAAMgB,CAAC,GAAID,eAAe,IACtBtB,cAAc,CAACwB,IAAI,CAACrB,YAAY,EAAEmB,eAAe,CAAC,GAAIA,eAAe,GAAG,UAAU;MACtF;MACA;MACA;MACApB,YAAY,CAACK,GAAG,CAAC,CAACkB,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACH,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9Cd,SAAS,CAACkB,MAAM,CAACd,GAAG,CAAC;MACrBZ,gBAAgB,CAACM,GAAG,CAAC;IACzB;EACJ,CAAC;EACD,OAAOK,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}